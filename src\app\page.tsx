'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Trophy, Zap, Shield, Users, TrendingUp, Star } from 'lucide-react';
import Layout from '@/components/Layout/Layout';
import Button from '@/components/UI/Button';
import Card from '@/components/UI/Card';

export default function Home() {
  const features = [
    {
      icon: <Zap className="w-8 h-8" />,
      title: 'Live Betting',
      description: 'Bet on live matches with real-time odds and instant updates.',
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Secure & Safe',
      description: 'Your funds and data are protected with bank-level security.',
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: 'Best Odds',
      description: 'Competitive odds across all sports and betting markets.',
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: '24/7 Support',
      description: 'Round-the-clock customer support for all your needs.',
    },
  ];

  const stats = [
    { label: 'Active Users', value: '250K+' },
    { label: 'Sports Covered', value: '40+' },
    { label: 'Daily Matches', value: '1000+' },
    { label: 'Payout Rate', value: '98.5%' },
  ];

  const popularSports = [
    { name: 'Football', matches: 45, icon: '⚽' },
    { name: 'Basketball', matches: 32, icon: '🏀' },
    { name: 'Tennis', matches: 28, icon: '🎾' },
    { name: 'Baseball', matches: 18, icon: '⚾' },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              Welcome to{' '}
              <span className="gradient-text">TahsinBet</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/80 mb-8 max-w-3xl mx-auto">
              Experience the future of sports betting with live odds, secure transactions, 
              and the best betting experience in the industry.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                size="xl"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                Start Betting Now
              </Button>
              <Button
                variant="outline"
                size="xl"
                className="border-white/30 text-white hover:bg-white/10"
              >
                Watch Demo
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    {stat.value}
                  </div>
                  <div className="text-white/60">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Why Choose TahsinBet?
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              We provide the most comprehensive and secure betting platform 
              with features designed for both beginners and professionals.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              >
                <Card glass className="text-center h-full">
                  <div className="text-blue-400 mb-4 flex justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-white/70">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Sports Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Popular Sports
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Bet on your favorite sports with competitive odds and live updates.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {popularSports.map((sport, index) => (
              <motion.div
                key={sport.name}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card glass hover className="text-center">
                  <div className="text-4xl mb-4">{sport.icon}</div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {sport.name}
                  </h3>
                  <p className="text-white/70 mb-4">
                    {sport.matches} live matches
                  </p>
                  <Button variant="outline" size="sm" className="border-white/30 text-white hover:bg-white/10">
                    View Matches
                  </Button>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600/20 to-purple-600/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Start Winning?
            </h2>
            <p className="text-xl text-white/80 mb-8">
              Join thousands of satisfied customers and start your betting journey today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="xl" rightIcon={<ArrowRight className="w-5 h-5" />}>
                  Create Account
                </Button>
              </Link>
              <Link href="/sports">
                <Button variant="outline" size="xl" className="border-white/30 text-white hover:bg-white/10">
                  Explore Sports
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </Layout>
  );
}
