'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Play, Pause, Volume2, VolumeX, TrendingUp, Clock } from 'lucide-react';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';

export default function LiveBettingPage() {
  const [selectedMatch, setSelectedMatch] = useState(0);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const liveMatches = [
    {
      id: 1,
      sport: 'Football',
      league: 'Premier League',
      homeTeam: 'Manchester City',
      awayTeam: 'Arsenal',
      score: { home: 2, away: 1 },
      time: '67\'',
      homeOdds: { win: 1.45, draw: 4.20, lose: 6.50 },
      awayOdds: { win: 6.50, draw: 4.20, lose: 1.45 },
      events: [
        { time: '65\'', type: 'goal', team: 'home', player: 'Haaland' },
        { time: '43\'', type: 'goal', team: 'away', player: 'Saka' },
        { time: '23\'', type: 'goal', team: 'home', player: 'De Bruyne' },
      ],
      stats: {
        possession: { home: 58, away: 42 },
        shots: { home: 12, away: 8 },
        corners: { home: 6, away: 3 },
      },
    },
    {
      id: 2,
      sport: 'Basketball',
      league: 'NBA',
      homeTeam: 'Lakers',
      awayTeam: 'Warriors',
      score: { home: 89, away: 94 },
      time: 'Q3 8:45',
      homeOdds: { win: 2.10, spread: 1.90 },
      awayOdds: { win: 1.75, spread: 1.90 },
      events: [
        { time: '9:12', type: 'score', team: 'away', player: 'Curry 3PT' },
        { time: '9:45', type: 'score', team: 'home', player: 'LeBron 2PT' },
      ],
      stats: {
        fieldGoal: { home: '45%', away: '52%' },
        threePoint: { home: '38%', away: '41%' },
        rebounds: { home: 28, away: 31 },
      },
    },
    {
      id: 3,
      sport: 'Tennis',
      league: 'ATP Masters',
      homeTeam: 'Djokovic',
      awayTeam: 'Alcaraz',
      score: { home: '6-4, 3-2', away: '' },
      time: 'Set 2',
      homeOdds: { win: 1.65 },
      awayOdds: { win: 2.25 },
      events: [
        { time: 'Game 5', type: 'break', team: 'home', player: 'Djokovic breaks' },
        { time: 'Game 3', type: 'ace', team: 'away', player: 'Alcaraz ace' },
      ],
      stats: {
        aces: { home: 8, away: 12 },
        winners: { home: 15, away: 18 },
        unforced: { home: 7, away: 11 },
      },
    },
  ];

  const currentMatch = liveMatches[selectedMatch];

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Live Betting
            </h1>
            <p className="text-xl text-white/80">
              Bet on live matches with real-time odds and instant updates
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Live Matches Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="lg:col-span-1"
            >
              <Card glass className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Live Now</h3>
                  <div className="flex items-center space-x-1 text-red-400">
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                    <span className="text-sm">LIVE</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  {liveMatches.map((match, index) => (
                    <button
                      key={match.id}
                      onClick={() => setSelectedMatch(index)}
                      className={`w-full p-3 rounded-lg transition-all duration-200 text-left ${
                        selectedMatch === index
                          ? 'bg-blue-600 text-white'
                          : 'bg-white/5 text-white/80 hover:bg-white/10'
                      }`}
                    >
                      <div className="text-xs text-blue-400 mb-1">{match.league}</div>
                      <div className="font-medium text-sm">
                        {match.homeTeam} vs {match.awayTeam}
                      </div>
                      <div className="text-xs mt-1 opacity-75">{match.time}</div>
                    </button>
                  ))}
                </div>
              </Card>
            </motion.div>

            {/* Main Live Match */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-3"
            >
              <Card glass className="p-6 mb-6">
                {/* Match Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 text-red-400">
                      <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                      <span className="font-semibold">LIVE</span>
                    </div>
                    <span className="text-blue-400 font-medium">{currentMatch.league}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setIsAudioEnabled(!isAudioEnabled)}
                      className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                    >
                      {isAudioEnabled ? (
                        <Volume2 className="w-4 h-4 text-white" />
                      ) : (
                        <VolumeX className="w-4 h-4 text-white" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Score Display */}
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center space-x-8 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white mb-2">
                        {currentMatch.homeTeam}
                      </div>
                      <div className="text-4xl font-bold text-white">
                        {typeof currentMatch.score.home === 'number' 
                          ? currentMatch.score.home 
                          : currentMatch.score.home}
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-white/60 mb-2">VS</div>
                      <div className="text-2xl font-bold text-white">{currentMatch.time}</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white mb-2">
                        {currentMatch.awayTeam}
                      </div>
                      <div className="text-4xl font-bold text-white">
                        {typeof currentMatch.score.away === 'number' 
                          ? currentMatch.score.away 
                          : currentMatch.score.away || '0'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Live Odds */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <Card className="p-4 text-center bg-green-500/10 border-green-500/30">
                    <div className="text-green-400 font-semibold mb-2">
                      {currentMatch.homeTeam} Win
                    </div>
                    <div className="text-2xl font-bold text-white mb-2">
                      {currentMatch.homeOdds.win}
                    </div>
                    <Button size="sm" className="w-full bg-green-600 hover:bg-green-700">
                      Bet Now
                    </Button>
                  </Card>
                  
                  {currentMatch.homeOdds.draw && (
                    <Card className="p-4 text-center bg-yellow-500/10 border-yellow-500/30">
                      <div className="text-yellow-400 font-semibold mb-2">Draw</div>
                      <div className="text-2xl font-bold text-white mb-2">
                        {currentMatch.homeOdds.draw}
                      </div>
                      <Button size="sm" className="w-full bg-yellow-600 hover:bg-yellow-700">
                        Bet Now
                      </Button>
                    </Card>
                  )}
                  
                  <Card className="p-4 text-center bg-red-500/10 border-red-500/30">
                    <div className="text-red-400 font-semibold mb-2">
                      {currentMatch.awayTeam} Win
                    </div>
                    <div className="text-2xl font-bold text-white mb-2">
                      {currentMatch.awayOdds.win}
                    </div>
                    <Button size="sm" className="w-full bg-red-600 hover:bg-red-700">
                      Bet Now
                    </Button>
                  </Card>
                </div>

                {/* Recent Events */}
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-white mb-4">Recent Events</h4>
                  <div className="space-y-2">
                    {currentMatch.events.slice(0, 3).map((event, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg"
                      >
                        <div className="text-blue-400 font-mono text-sm">
                          {event.time}
                        </div>
                        <div className="flex-1 text-white">
                          {event.type === 'goal' && '⚽'} 
                          {event.type === 'score' && '🏀'} 
                          {event.type === 'break' && '🎾'} 
                          {event.type === 'ace' && '🎾'} 
                          {event.player}
                        </div>
                        <div className={`w-3 h-3 rounded-full ${
                          event.team === 'home' ? 'bg-green-400' : 'bg-red-400'
                        }`}></div>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Match Statistics */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-4">Match Statistics</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(currentMatch.stats).map(([key, value]) => (
                      <div key={key} className="bg-white/5 rounded-lg p-4">
                        <div className="text-white/60 text-sm mb-2 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-white font-semibold">
                            {typeof value.home === 'number' ? value.home : value.home}
                          </span>
                          <span className="text-white font-semibold">
                            {typeof value.away === 'number' ? value.away : value.away}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
