'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Trash2, Calculator, TrendingUp, AlertCircle, Check } from 'lucide-react';
import { useBetting } from '@/contexts/BettingContext';
import Button from '@/components/UI/Button';
import Card from '@/components/UI/Card';
import { validateBetSlip, formatValidationErrors } from '@/utils/bettingValidation';

const BetSlip = () => {
  const { state, removeFromBetSlip, updateBetAmount, clearBetSlip, setBetType, placeBets, toggleBetSlip } = useBetting();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const quickAmounts = [10, 25, 50, 100];

  const handleQuickAmount = (itemId: string, amount: number) => {
    updateBetAmount(itemId, amount);
  };

  const handleAmountChange = (itemId: string, value: string) => {
    const amount = parseFloat(value) || 0;
    if (amount >= 0 && amount <= state.bettingLimits.maxBet) {
      updateBetAmount(itemId, amount);
    }
  };

  const getValidationErrors = () => {
    return validateBetSlip(
      state.betSlip.items,
      state.userBalance,
      state.bettingLimits,
      state.betSlip.betType
    );
  };

  const canPlaceBets = () => {
    const errors = getValidationErrors();
    return errors.length === 0 && acceptedTerms;
  };

  const handlePlaceBets = async () => {
    if (!acceptedTerms) {
      setShowConfirmation(true);
      return;
    }
    await placeBets();
  };

  const confirmPlaceBets = async () => {
    setShowConfirmation(false);
    await placeBets();
  };

  if (!state.isSlipOpen) return null;

  return (
    <>
      {/* Overlay */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
        onClick={toggleBetSlip}
      />

      {/* Bet Slip */}
      <motion.div
        initial={{ x: '100%' }}
        animate={{ x: 0 }}
        exit={{ x: '100%' }}
        transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        className="fixed right-0 top-0 h-full w-full max-w-md bg-slate-900 border-l border-white/20 z-50 overflow-hidden flex flex-col"
      >
        {/* Header */}
        <div className="p-4 border-b border-white/20 bg-slate-800/50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">Bet Slip</h3>
            <div className="flex items-center space-x-2">
              {state.betSlip.items.length > 0 && (
                <button
                  onClick={clearBetSlip}
                  className="p-2 text-white/60 hover:text-red-400 transition-colors"
                  title="Clear all bets"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={toggleBetSlip}
                className="p-2 text-white/60 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Bet Type Toggle */}
          {state.betSlip.items.length > 1 && (
            <div className="mt-3 flex bg-white/10 rounded-lg p-1">
              <button
                onClick={() => setBetType('single')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  state.betSlip.betType === 'single'
                    ? 'bg-blue-600 text-white'
                    : 'text-white/80 hover:text-white'
                }`}
              >
                Single Bets
              </button>
              <button
                onClick={() => setBetType('parlay')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  state.betSlip.betType === 'parlay'
                    ? 'bg-blue-600 text-white'
                    : 'text-white/80 hover:text-white'
                }`}
              >
                Parlay
              </button>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {state.betSlip.items.length === 0 ? (
            <div className="p-6 text-center">
              <Calculator className="w-12 h-12 text-white/40 mx-auto mb-4" />
              <p className="text-white/60 mb-2">Your bet slip is empty</p>
              <p className="text-white/40 text-sm">
                Click on odds to add selections to your bet slip
              </p>
            </div>
          ) : (
            <div className="p-4 space-y-4">
              {state.betSlip.items.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card glass className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="text-blue-400 text-xs font-medium mb-1">
                          {item.match.league}
                        </div>
                        <div className="text-white font-medium text-sm mb-1">
                          {item.match.homeTeam} vs {item.match.awayTeam}
                        </div>
                        <div className="text-white/80 text-sm">
                          {item.selection.label}
                        </div>
                      </div>
                      <button
                        onClick={() => removeFromBetSlip(item.id)}
                        className="p-1 text-white/60 hover:text-red-400 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <span className="text-white/60 text-sm">Odds</span>
                      <span className="text-white font-semibold">
                        {item.selection.odds.toFixed(2)}
                      </span>
                    </div>

                    {/* Quick Amount Buttons */}
                    <div className="grid grid-cols-4 gap-1 mb-3">
                      {quickAmounts.map((amount) => (
                        <button
                          key={amount}
                          onClick={() => handleQuickAmount(item.id, amount)}
                          className="py-1 px-2 bg-white/10 hover:bg-white/20 text-white text-xs rounded transition-colors"
                        >
                          €{amount}
                        </button>
                      ))}
                    </div>

                    {/* Amount Input */}
                    <div className="mb-3">
                      <label className="block text-white/60 text-xs mb-1">
                        Stake (€{state.bettingLimits.minBet} - €{state.bettingLimits.maxBet})
                      </label>
                      <input
                        type="number"
                        min={state.bettingLimits.minBet}
                        max={state.bettingLimits.maxBet}
                        step="0.01"
                        value={item.amount || ''}
                        onChange={(e) => handleAmountChange(item.id, e.target.value)}
                        className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-blue-500"
                        placeholder="Enter amount"
                      />
                    </div>

                    {/* Potential Win */}
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/60">Potential Win</span>
                      <span className="text-green-400 font-semibold">
                        €{item.potentialWin.toFixed(2)}
                      </span>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {state.betSlip.items.length > 0 && (
          <div className="p-4 border-t border-white/20 bg-slate-800/50">
            {/* Parlay Info */}
            {state.betSlip.betType === 'parlay' && state.betSlip.parlayOdds && (
              <div className="mb-4 p-3 bg-purple-500/20 border border-purple-500/30 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-purple-300">Parlay Odds</span>
                  <span className="text-purple-300 font-semibold">
                    {state.betSlip.parlayOdds.toFixed(2)}
                  </span>
                </div>
              </div>
            )}

            {/* Totals */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center justify-between">
                <span className="text-white/60">Total Stake</span>
                <span className="text-white font-semibold">
                  €{state.betSlip.totalStake.toFixed(2)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/60">Potential Win</span>
                <span className="text-green-400 font-semibold">
                  €{state.betSlip.totalPotentialWin.toFixed(2)}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/60">Your Balance</span>
                <span className="text-white">€{state.userBalance.toFixed(2)}</span>
              </div>
            </div>

            {/* Validation Messages */}
            {(() => {
              const validationErrors = getValidationErrors();
              const errorMessages = formatValidationErrors(validationErrors);

              return errorMessages.map((message, index) => (
                <div key={index} className="mb-3 p-2 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center space-x-2">
                  <AlertCircle className="w-4 h-4 text-red-400" />
                  <span className="text-red-300 text-sm">{message}</span>
                </div>
              ));
            })()}

            {/* Place Bet Button */}
            <Button
              fullWidth
              size="lg"
              disabled={!canPlaceBets()}
              isLoading={state.isLoading}
              onClick={handlePlaceBets}
              leftIcon={<TrendingUp className="w-4 h-4" />}
            >
              Place Bet{state.betSlip.items.length > 1 ? 's' : ''}
            </Button>

            {/* Terms Checkbox */}
            <label className="flex items-start space-x-2 mt-3 text-xs text-white/60">
              <input
                type="checkbox"
                checked={acceptedTerms}
                onChange={(e) => setAcceptedTerms(e.target.checked)}
                className="w-3 h-3 mt-0.5 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500"
              />
              <span>
                I accept the{' '}
                <a href="/terms" className="text-blue-400 hover:text-blue-300">
                  terms and conditions
                </a>
              </span>
            </label>
          </div>
        )}
      </motion.div>

      {/* Confirmation Modal */}
      <AnimatePresence>
        {showConfirmation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-slate-800 rounded-xl p-6 max-w-md w-full"
            >
              <div className="text-center mb-6">
                <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Check className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Confirm Your Bet{state.betSlip.items.length > 1 ? 's' : ''}
                </h3>
                <p className="text-white/60">
                  You are about to place {state.betSlip.items.length} bet{state.betSlip.items.length > 1 ? 's' : ''} 
                  for a total stake of €{state.betSlip.totalStake.toFixed(2)}
                </p>
              </div>

              <div className="space-y-3 mb-6">
                <label className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={acceptedTerms}
                    onChange={(e) => setAcceptedTerms(e.target.checked)}
                    className="w-4 h-4 mt-0.5 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500"
                  />
                  <span className="text-white/80 text-sm">
                    I confirm that I am 18+ and accept the terms and conditions
                  </span>
                </label>
              </div>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  fullWidth
                  onClick={() => setShowConfirmation(false)}
                  className="border-white/30 text-white hover:bg-white/10"
                >
                  Cancel
                </Button>
                <Button
                  fullWidth
                  disabled={!acceptedTerms}
                  onClick={confirmPlaceBets}
                  isLoading={state.isLoading}
                >
                  Confirm Bet
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default BetSlip;
