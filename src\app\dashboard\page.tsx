'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Clock, 
  Trophy,
  Activity,
  Calendar,
  BarChart3,
  PieChart
} from 'lucide-react';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import CashOut from '@/components/Betting/CashOut';
import { useBetting } from '@/contexts/BettingContext';

export default function DashboardPage() {
  const [timeRange, setTimeRange] = useState('7d');
  const { state } = useBetting();

  const userStats = {
    balance: state.userBalance,
    totalBets: 47 + state.activeBets.length,
    activeBets: state.activeBets.length,
    totalWinnings: 2840.50,
    winRate: 68.5,
    favoriteSport: 'Football',
  };

  const recentBets = [
    {
      id: 1,
      match: 'Manchester United vs Liverpool',
      type: 'Win',
      team: 'Manchester United',
      amount: 50,
      odds: 2.45,
      potentialWin: 122.50,
      status: 'won',
      date: '2024-01-14',
    },
    {
      id: 2,
      match: 'Lakers vs Warriors',
      type: 'Spread',
      team: 'Lakers +5.5',
      amount: 75,
      odds: 1.90,
      potentialWin: 142.50,
      status: 'pending',
      date: '2024-01-15',
    },
    {
      id: 3,
      match: 'Real Madrid vs Barcelona',
      type: 'Over/Under',
      team: 'Over 2.5',
      amount: 100,
      odds: 1.85,
      potentialWin: 185.00,
      status: 'lost',
      date: '2024-01-13',
    },
  ];

  const upcomingMatches = [
    {
      id: 1,
      homeTeam: 'Chelsea',
      awayTeam: 'Arsenal',
      league: 'Premier League',
      time: '15:00',
      date: '2024-01-16',
      homeOdds: 2.20,
      drawOdds: 3.40,
      awayOdds: 3.10,
    },
    {
      id: 2,
      homeTeam: 'Celtics',
      awayTeam: 'Heat',
      league: 'NBA',
      time: '20:00',
      date: '2024-01-16',
      homeOdds: 1.75,
      awayOdds: 2.10,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'won': return 'text-green-400 bg-green-400/20';
      case 'lost': return 'text-red-400 bg-red-400/20';
      case 'pending': return 'text-yellow-400 bg-yellow-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'won': return <TrendingUp className="w-4 h-4" />;
      case 'lost': return <TrendingDown className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'cashed_out': return <DollarSign className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const handleCashOut = async (betId: string, amount: number) => {
    // Simulate cash out API call
    console.log(`Cashing out bet ${betId} for €${amount}`);
    // In a real app, this would update the bet status and user balance
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">Dashboard</h1>
                <p className="text-white/60">Welcome back! Here's your betting overview.</p>
              </div>
              
              <div className="flex items-center space-x-2 mt-4 md:mt-0">
                {['24h', '7d', '30d', '90d'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      timeRange === range
                        ? 'bg-blue-600 text-white'
                        : 'bg-white/10 text-white/80 hover:bg-white/20'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Stats Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            <Card glass className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Balance</p>
                  <p className="text-2xl font-bold text-white">${userStats.balance}</p>
                </div>
                <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </Card>

            <Card glass className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Total Bets</p>
                  <p className="text-2xl font-bold text-white">{userStats.totalBets}</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-blue-400" />
                </div>
              </div>
            </Card>

            <Card glass className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Win Rate</p>
                  <p className="text-2xl font-bold text-white">{userStats.winRate}%</p>
                </div>
                <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <Trophy className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </Card>

            <Card glass className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Total Winnings</p>
                  <p className="text-2xl font-bold text-white">${userStats.totalWinnings}</p>
                </div>
                <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-yellow-400" />
                </div>
              </div>
            </Card>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Active Bets with Cash Out */}
            {state.activeBets.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                className="lg:col-span-3 mb-8"
              >
                <Card glass className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-6">Active Bets</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {state.activeBets.map((bet, index) => (
                      <motion.div
                        key={bet.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                      >
                        <CashOut bet={bet} onCashOut={handleCashOut} />
                      </motion.div>
                    ))}
                  </div>
                </Card>
              </motion.div>
            )}

            {/* Recent Bets */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Card glass className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-white">Recent Bets</h3>
                  <Button variant="outline" size="sm" className="border-white/30 text-white hover:bg-white/10">
                    View All
                  </Button>
                </div>

                <div className="space-y-4">
                  {recentBets.map((bet, index) => (
                    <motion.div
                      key={bet.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-white/5 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(bet.status)}`}>
                            {getStatusIcon(bet.status)}
                            <span className="capitalize">{bet.status}</span>
                          </span>
                          <span className="text-white/60 text-sm">{bet.date}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-white font-semibold">${bet.amount}</div>
                          <div className="text-white/60 text-sm">@ {bet.odds}</div>
                        </div>
                      </div>
                      
                      <div className="text-white font-medium mb-1">{bet.match}</div>
                      <div className="text-white/80 text-sm">{bet.type}: {bet.team}</div>
                      
                      {bet.status === 'pending' && (
                        <div className="mt-2 text-green-400 text-sm">
                          Potential win: ${bet.potentialWin}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </Card>
            </motion.div>

            {/* Upcoming Matches */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <Card glass className="p-6">
                <h3 className="text-xl font-semibold text-white mb-6">Upcoming Matches</h3>
                
                <div className="space-y-4">
                  {upcomingMatches.map((match, index) => (
                    <motion.div
                      key={match.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-white/5 rounded-lg p-4"
                    >
                      <div className="text-blue-400 text-xs font-medium mb-2">{match.league}</div>
                      <div className="text-white font-medium mb-2">
                        {match.homeTeam} vs {match.awayTeam}
                      </div>
                      <div className="flex items-center space-x-1 text-white/60 text-sm mb-3">
                        <Calendar className="w-4 h-4" />
                        <span>{match.date} at {match.time}</span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-green-500/50 text-green-400 hover:bg-green-500/20 text-xs"
                        >
                          {match.homeOdds}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-red-500/50 text-red-400 hover:bg-red-500/20 text-xs"
                        >
                          {match.awayOdds}
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <Button fullWidth className="mt-6">
                  View All Matches
                </Button>
              </Card>
            </motion.div>
          </div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-8"
          >
            <Card glass className="p-6">
              <h3 className="text-xl font-semibold text-white mb-6">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button className="flex items-center justify-center space-x-2">
                  <DollarSign className="w-4 h-4" />
                  <span>Deposit</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2 border-white/30 text-white hover:bg-white/10">
                  <TrendingUp className="w-4 h-4" />
                  <span>Withdraw</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2 border-white/30 text-white hover:bg-white/10">
                  <BarChart3 className="w-4 h-4" />
                  <span>Statistics</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2 border-white/30 text-white hover:bg-white/10">
                  <PieChart className="w-4 h-4" />
                  <span>Reports</span>
                </Button>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
}
