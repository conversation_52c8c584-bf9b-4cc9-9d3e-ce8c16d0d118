export interface User {
  id: string;
  email: string;
  username: string;
  balance: number;
  avatar?: string;
  createdAt: Date;
}

export interface Sport {
  id: string;
  name: string;
  icon: string;
  category: string;
  isPopular: boolean;
}

export interface Match {
  id: string;
  sport: string;
  homeTeam: string;
  awayTeam: string;
  homeOdds: number;
  awayOdds: number;
  drawOdds?: number;
  startTime: Date;
  isLive: boolean;
  score?: {
    home: number;
    away: number;
  };
  league: string;
}

export interface Bet {
  id: string;
  userId: string;
  matchId: string;
  type: 'home' | 'away' | 'draw';
  amount: number;
  odds: number;
  potentialWin: number;
  status: 'pending' | 'won' | 'lost';
  placedAt: Date;
}

export interface LiveEvent {
  id: string;
  matchId: string;
  type: 'goal' | 'card' | 'substitution' | 'corner' | 'penalty';
  description: string;
  timestamp: Date;
  team?: 'home' | 'away';
}

export interface AuthFormData {
  email: string;
  password: string;
  username?: string;
  confirmPassword?: string;
}

export interface FormErrors {
  [key: string]: string;
}

export interface DashboardStats {
  totalBets: number;
  activeBets: number;
  totalWinnings: number;
  winRate: number;
  favoritesSport: string;
}

export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
  isActive?: boolean;
}
