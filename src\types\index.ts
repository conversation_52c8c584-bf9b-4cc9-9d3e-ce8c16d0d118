export interface User {
  id: string;
  email: string;
  username: string;
  balance: number;
  avatar?: string;
  createdAt: Date;
}

export interface Sport {
  id: string;
  name: string;
  icon: string;
  category: string;
  isPopular: boolean;
}

export interface Match {
  id: string;
  sport: string;
  homeTeam: string;
  awayTeam: string;
  homeOdds: number;
  awayOdds: number;
  drawOdds?: number;
  startTime: Date;
  isLive: boolean;
  score?: {
    home: number;
    away: number;
  };
  league: string;
}

export interface Bet {
  id: string;
  userId: string;
  matchId: string;
  type: 'home' | 'away' | 'draw' | 'over' | 'under' | 'handicap';
  amount: number;
  odds: number;
  potentialWin: number;
  status: 'pending' | 'won' | 'lost' | 'cashed_out';
  placedAt: Date;
  settledAt?: Date;
  cashOutValue?: number;
  isLive?: boolean;
}

export interface BetSlipItem {
  id: string;
  matchId: string;
  match: {
    homeTeam: string;
    awayTeam: string;
    league: string;
    startTime: Date;
    isLive: boolean;
  };
  selection: {
    type: 'home' | 'away' | 'draw' | 'over' | 'under' | 'handicap';
    label: string;
    odds: number;
    line?: number; // For handicap/over-under
  };
  amount: number;
  potentialWin: number;
}

export interface BetSlip {
  items: BetSlipItem[];
  totalStake: number;
  totalPotentialWin: number;
  betType: 'single' | 'parlay';
  parlayOdds?: number;
}

export interface BettingMarket {
  id: string;
  name: string;
  type: 'match_result' | 'over_under' | 'handicap' | 'both_teams_score';
  selections: BettingSelection[];
}

export interface BettingSelection {
  id: string;
  label: string;
  odds: number;
  type: 'home' | 'away' | 'draw' | 'over' | 'under' | 'yes' | 'no';
  line?: number;
  isAvailable: boolean;
}

export interface LiveEvent {
  id: string;
  matchId: string;
  type: 'goal' | 'card' | 'substitution' | 'corner' | 'penalty';
  description: string;
  timestamp: Date;
  team?: 'home' | 'away';
}

export interface AuthFormData {
  email: string;
  password: string;
  username?: string;
  confirmPassword?: string;
}

export interface FormErrors {
  [key: string]: string;
}

export interface DashboardStats {
  totalBets: number;
  activeBets: number;
  totalWinnings: number;
  winRate: number;
  favoritesSport: string;
}

export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
  isActive?: boolean;
}

export interface BettingLimits {
  minBet: number;
  maxBet: number;
  maxPayout: number;
  maxParlayLegs: number;
}

export interface OddsUpdate {
  matchId: string;
  marketType: string;
  selectionType: string;
  newOdds: number;
  timestamp: Date;
}

export interface CashOutOffer {
  betId: string;
  amount: number;
  percentage: number;
  expiresAt: Date;
}

export interface BetValidationError {
  field: string;
  message: string;
  code: string;
}
