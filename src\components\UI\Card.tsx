'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  glass?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  onClick?: () => void;
}

const Card = ({
  children,
  className = '',
  hover = true,
  glass = false,
  padding = 'md',
  onClick,
}: CardProps) => {
  const baseClasses = 'rounded-xl border transition-all duration-300';
  
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };
  
  const glassClasses = glass
    ? 'bg-white/10 backdrop-blur-md border-white/20 shadow-xl'
    : 'bg-white border-gray-200 shadow-lg';
    
  const hoverClasses = hover
    ? 'hover:shadow-xl hover:-translate-y-1 cursor-pointer'
    : '';
    
  const combinedClasses = `${baseClasses} ${glassClasses} ${paddingClasses[padding]} ${hoverClasses} ${className}`;
  
  const CardComponent = onClick ? motion.div : 'div';
  
  return (
    <CardComponent
      className={combinedClasses}
      onClick={onClick}
      {...(onClick && {
        whileHover: { scale: 1.02 },
        whileTap: { scale: 0.98 },
      })}
    >
      {children}
    </CardComponent>
  );
};

export default Card;
