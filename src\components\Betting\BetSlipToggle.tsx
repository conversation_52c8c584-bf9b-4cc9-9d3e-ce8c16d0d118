'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ShoppingCart, X } from 'lucide-react';
import { useBetting } from '@/contexts/BettingContext';

const BetSlipToggle = () => {
  const { state, toggleBetSlip } = useBetting();

  if (state.betSlip.items.length === 0) return null;

  return (
    <AnimatePresence>
      <motion.button
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0, opacity: 0 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={toggleBetSlip}
        className="fixed bottom-6 right-6 z-30 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 lg:hidden"
      >
        <div className="relative p-4">
          {/* Cart Icon */}
          <ShoppingCart className="w-6 h-6" />
          
          {/* Badge */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center"
          >
            {state.betSlip.items.length}
          </motion.div>

          {/* Pulse animation for new items */}
          <motion.div
            className="absolute inset-0 bg-white/20 rounded-full"
            initial={{ scale: 1, opacity: 0 }}
            animate={{ scale: 1.5, opacity: 0 }}
            transition={{ duration: 0.6, repeat: Infinity }}
          />
        </div>

        {/* Total stake indicator */}
        {state.betSlip.totalStake > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap"
          >
            €{state.betSlip.totalStake.toFixed(2)}
          </motion.div>
        )}
      </motion.button>
    </AnimatePresence>
  );
};

export default BetSlipToggle;
