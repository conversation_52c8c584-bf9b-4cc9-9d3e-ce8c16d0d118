{"version": 3, "sources": ["../../../src/server/base-http/web.ts"], "names": ["toNodeOutgoingHttpHeaders", "BaseNextRequest", "BaseNextResponse", "Detached<PERSON>romise", "WebNextRequest", "constructor", "request", "url", "URL", "method", "href", "slice", "origin", "length", "clone", "body", "headers", "name", "value", "entries", "parseBody", "_limit", "Error", "WebNextResponse", "transformStream", "TransformStream", "writable", "Headers", "textBody", "undefined", "sendPromise", "_sent", "<PERSON><PERSON><PERSON><PERSON>", "delete", "val", "Array", "isArray", "append", "removeHeader", "getHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON>", "split", "map", "v", "trimStart", "get", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "has", "append<PERSON><PERSON>er", "send", "resolve", "sent", "toResponse", "promise", "Response", "readable", "status", "statusCode", "statusText", "statusMessage"], "mappings": "AAGA,SAASA,yBAAyB,QAAQ,eAAc;AACxD,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,UAAS;AAC3D,SAASC,eAAe,QAAQ,6BAA4B;AAE5D,OAAO,MAAMC,uBAAuBH;IAKlCI,YAAYC,OAAgB,CAAE;QAC5B,MAAMC,MAAM,IAAIC,IAAIF,QAAQC,GAAG;QAE/B,KAAK,CACHD,QAAQG,MAAM,EACdF,IAAIG,IAAI,CAACC,KAAK,CAACJ,IAAIK,MAAM,CAACC,MAAM,GAChCP,QAAQQ,KAAK,GAAGC,IAAI;QAEtB,IAAI,CAACT,OAAO,GAAGA;QAEf,IAAI,CAACU,OAAO,GAAG,CAAC;QAChB,KAAK,MAAM,CAACC,MAAMC,MAAM,IAAIZ,QAAQU,OAAO,CAACG,OAAO,GAAI;YACrD,IAAI,CAACH,OAAO,CAACC,KAAK,GAAGC;QACvB;IACF;IAEA,MAAME,UAAUC,MAAuB,EAAgB;QACrD,MAAM,IAAIC,MAAM;IAClB;AACF;AAEA,OAAO,MAAMC,wBAAwBrB;IAOnCG,YAAmBmB,kBAAkB,IAAIC,iBAAiB,CAAE;QAC1D,KAAK,CAACD,gBAAgBE,QAAQ;+BADbF;aANXR,UAAU,IAAIW;aACdC,WAA+BC;aAmDtBC,cAAc,IAAI3B;aAC3B4B,QAAQ;IA7ChB;IAEAC,UAAUf,IAAY,EAAEC,KAAwB,EAAQ;QACtD,IAAI,CAACF,OAAO,CAACiB,MAAM,CAAChB;QACpB,KAAK,MAAMiB,OAAOC,MAAMC,OAAO,CAAClB,SAASA,QAAQ;YAACA;SAAM,CAAE;YACxD,IAAI,CAACF,OAAO,CAACqB,MAAM,CAACpB,MAAMiB;QAC5B;QACA,OAAO,IAAI;IACb;IAEAI,aAAarB,IAAY,EAAQ;QAC/B,IAAI,CAACD,OAAO,CAACiB,MAAM,CAAChB;QACpB,OAAO,IAAI;IACb;IAEAsB,gBAAgBtB,IAAY,EAAwB;YAE3C;QADP,iEAAiE;QACjE,QAAO,kBAAA,IAAI,CAACuB,SAAS,CAACvB,0BAAf,gBACHwB,KAAK,CAAC,KACPC,GAAG,CAAC,CAACC,IAAMA,EAAEC,SAAS;IAC3B;IAEAJ,UAAUvB,IAAY,EAAsB;QAC1C,OAAO,IAAI,CAACD,OAAO,CAAC6B,GAAG,CAAC5B,SAASY;IACnC;IAEAiB,aAAkC;QAChC,OAAO9C,0BAA0B,IAAI,CAACgB,OAAO;IAC/C;IAEA+B,UAAU9B,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACD,OAAO,CAACgC,GAAG,CAAC/B;IAC1B;IAEAgC,aAAahC,IAAY,EAAEC,KAAa,EAAQ;QAC9C,IAAI,CAACF,OAAO,CAACqB,MAAM,CAACpB,MAAMC;QAC1B,OAAO,IAAI;IACb;IAEAH,KAAKG,KAAa,EAAE;QAClB,IAAI,CAACU,QAAQ,GAAGV;QAChB,OAAO,IAAI;IACb;IAIOgC,OAAO;QACZ,IAAI,CAACpB,WAAW,CAACqB,OAAO;QACxB,IAAI,CAACpB,KAAK,GAAG;IACf;IAEA,IAAIqB,OAAO;QACT,OAAO,IAAI,CAACrB,KAAK;IACnB;IAEA,MAAasB,aAAa;QACxB,6DAA6D;QAC7D,IAAI,CAAC,IAAI,CAACD,IAAI,EAAE,MAAM,IAAI,CAACtB,WAAW,CAACwB,OAAO;QAE9C,OAAO,IAAIC,SAAS,IAAI,CAAC3B,QAAQ,IAAI,IAAI,CAACJ,eAAe,CAACgC,QAAQ,EAAE;YAClExC,SAAS,IAAI,CAACA,OAAO;YACrByC,QAAQ,IAAI,CAACC,UAAU;YACvBC,YAAY,IAAI,CAACC,aAAa;QAChC;IACF;AACF"}