'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { BetSlip, BetSlipItem, Bet, BettingLimits, OddsUpdate } from '@/types';

interface BettingState {
  betSlip: BetSlip;
  userBalance: number;
  bettingLimits: BettingLimits;
  activeBets: Bet[];
  isSlipOpen: boolean;
  isLoading: boolean;
  error: string | null;
}

type BettingAction =
  | { type: 'ADD_TO_SLIP'; payload: BetSlipItem }
  | { type: 'REMOVE_FROM_SLIP'; payload: string }
  | { type: 'UPDATE_AMOUNT'; payload: { id: string; amount: number } }
  | { type: 'CLEAR_SLIP' }
  | { type: 'TOGGLE_SLIP' }
  | { type: 'SET_BET_TYPE'; payload: 'single' | 'parlay' }
  | { type: 'PLACE_BETS_START' }
  | { type: 'PLACE_BETS_SUCCESS'; payload: Bet[] }
  | { type: 'PLACE_BETS_ERROR'; payload: string }
  | { type: 'UPDATE_BALANCE'; payload: number }
  | { type: 'UPDATE_ODDS'; payload: OddsUpdate }
  | { type: 'SET_ERROR'; payload: string | null };

const initialState: BettingState = {
  betSlip: {
    items: [],
    totalStake: 0,
    totalPotentialWin: 0,
    betType: 'single',
  },
  userBalance: 1250.75,
  bettingLimits: {
    minBet: 1,
    maxBet: 1000,
    maxPayout: 10000,
    maxParlayLegs: 10,
  },
  activeBets: [],
  isSlipOpen: false,
  isLoading: false,
  error: null,
};

function bettingReducer(state: BettingState, action: BettingAction): BettingState {
  switch (action.type) {
    case 'ADD_TO_SLIP': {
      const existingIndex = state.betSlip.items.findIndex(
        item => item.id === action.payload.id
      );
      
      let newItems;
      if (existingIndex >= 0) {
        // Replace existing item
        newItems = [...state.betSlip.items];
        newItems[existingIndex] = action.payload;
      } else {
        // Add new item
        newItems = [...state.betSlip.items, action.payload];
      }

      const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);
      
      return {
        ...state,
        betSlip: newBetSlip,
        isSlipOpen: true,
      };
    }

    case 'REMOVE_FROM_SLIP': {
      const newItems = state.betSlip.items.filter(item => item.id !== action.payload);
      const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);
      
      return {
        ...state,
        betSlip: newBetSlip,
      };
    }

    case 'UPDATE_AMOUNT': {
      const newItems = state.betSlip.items.map(item =>
        item.id === action.payload.id
          ? { 
              ...item, 
              amount: action.payload.amount,
              potentialWin: action.payload.amount * item.selection.odds
            }
          : item
      );
      
      const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);
      
      return {
        ...state,
        betSlip: newBetSlip,
      };
    }

    case 'CLEAR_SLIP':
      return {
        ...state,
        betSlip: {
          items: [],
          totalStake: 0,
          totalPotentialWin: 0,
          betType: state.betSlip.betType,
        },
      };

    case 'TOGGLE_SLIP':
      return {
        ...state,
        isSlipOpen: !state.isSlipOpen,
      };

    case 'SET_BET_TYPE': {
      const newBetSlip = calculateBetSlip(state.betSlip.items, action.payload);
      return {
        ...state,
        betSlip: newBetSlip,
      };
    }

    case 'PLACE_BETS_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'PLACE_BETS_SUCCESS':
      return {
        ...state,
        isLoading: false,
        activeBets: [...state.activeBets, ...action.payload],
        betSlip: {
          items: [],
          totalStake: 0,
          totalPotentialWin: 0,
          betType: state.betSlip.betType,
        },
        userBalance: state.userBalance - state.betSlip.totalStake,
      };

    case 'PLACE_BETS_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };

    case 'UPDATE_BALANCE':
      return {
        ...state,
        userBalance: action.payload,
      };

    case 'UPDATE_ODDS': {
      const newItems = state.betSlip.items.map(item => {
        if (item.matchId === action.payload.matchId && 
            item.selection.type === action.payload.selectionType) {
          return {
            ...item,
            selection: {
              ...item.selection,
              odds: action.payload.newOdds,
            },
            potentialWin: item.amount * action.payload.newOdds,
          };
        }
        return item;
      });

      const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);
      
      return {
        ...state,
        betSlip: newBetSlip,
      };
    }

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };

    default:
      return state;
  }
}

function calculateBetSlip(items: BetSlipItem[], betType: 'single' | 'parlay'): BetSlip {
  const totalStake = items.reduce((sum, item) => sum + item.amount, 0);
  
  let totalPotentialWin = 0;
  let parlayOdds = 1;

  if (betType === 'single') {
    totalPotentialWin = items.reduce((sum, item) => sum + item.potentialWin, 0);
  } else if (betType === 'parlay' && items.length > 0) {
    parlayOdds = items.reduce((odds, item) => odds * item.selection.odds, 1);
    totalPotentialWin = totalStake * parlayOdds;
  }

  return {
    items,
    totalStake,
    totalPotentialWin,
    betType,
    parlayOdds: betType === 'parlay' ? parlayOdds : undefined,
  };
}

interface BettingContextType {
  state: BettingState;
  addToBetSlip: (item: BetSlipItem) => void;
  removeFromBetSlip: (id: string) => void;
  updateBetAmount: (id: string, amount: number) => void;
  clearBetSlip: () => void;
  toggleBetSlip: () => void;
  setBetType: (type: 'single' | 'parlay') => void;
  placeBets: () => Promise<void>;
  updateOdds: (update: OddsUpdate) => void;
}

const BettingContext = createContext<BettingContextType | undefined>(undefined);

export function BettingProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(bettingReducer, initialState);

  const addToBetSlip = (item: BetSlipItem) => {
    dispatch({ type: 'ADD_TO_SLIP', payload: item });
  };

  const removeFromBetSlip = (id: string) => {
    dispatch({ type: 'REMOVE_FROM_SLIP', payload: id });
  };

  const updateBetAmount = (id: string, amount: number) => {
    dispatch({ type: 'UPDATE_AMOUNT', payload: { id, amount } });
  };

  const clearBetSlip = () => {
    dispatch({ type: 'CLEAR_SLIP' });
  };

  const toggleBetSlip = () => {
    dispatch({ type: 'TOGGLE_SLIP' });
  };

  const setBetType = (type: 'single' | 'parlay') => {
    dispatch({ type: 'SET_BET_TYPE', payload: type });
  };

  const placeBets = async () => {
    dispatch({ type: 'PLACE_BETS_START' });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create bet objects
      const newBets: Bet[] = state.betSlip.items.map(item => ({
        id: Math.random().toString(36).substr(2, 9),
        userId: 'user-1',
        matchId: item.matchId,
        type: item.selection.type,
        amount: item.amount,
        odds: item.selection.odds,
        potentialWin: item.potentialWin,
        status: 'pending' as const,
        placedAt: new Date(),
        isLive: item.match.isLive,
      }));

      dispatch({ type: 'PLACE_BETS_SUCCESS', payload: newBets });
    } catch (error) {
      dispatch({ type: 'PLACE_BETS_ERROR', payload: 'Failed to place bets' });
    }
  };

  const updateOdds = (update: OddsUpdate) => {
    dispatch({ type: 'UPDATE_ODDS', payload: update });
  };

  // Simulate real-time odds updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.betSlip.items.length > 0) {
        const randomItem = state.betSlip.items[Math.floor(Math.random() * state.betSlip.items.length)];
        const oddsChange = (Math.random() - 0.5) * 0.2; // ±0.1 change
        const newOdds = Math.max(1.01, randomItem.selection.odds + oddsChange);
        
        updateOdds({
          matchId: randomItem.matchId,
          marketType: 'match_result',
          selectionType: randomItem.selection.type,
          newOdds: Math.round(newOdds * 100) / 100,
          timestamp: new Date(),
        });
      }
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [state.betSlip.items]);

  return (
    <BettingContext.Provider
      value={{
        state,
        addToBetSlip,
        removeFromBetSlip,
        updateBetAmount,
        clearBetSlip,
        toggleBetSlip,
        setBetType,
        placeBets,
        updateOdds,
      }}
    >
      {children}
    </BettingContext.Provider>
  );
}

export function useBetting() {
  const context = useContext(BettingContext);
  if (context === undefined) {
    throw new Error('useBetting must be used within a BettingProvider');
  }
  return context;
}
