'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DollarSign, TrendingUp, TrendingDown, Clock, AlertCircle } from 'lucide-react';
import { Bet, CashOutOffer } from '@/types';
import Button from '@/components/UI/Button';
import Card from '@/components/UI/Card';

interface CashOutProps {
  bet: Bet;
  onCashOut: (betId: string, amount: number) => Promise<void>;
}

const CashOut = ({ bet, onCashOut }: CashOutProps) => {
  const [cashOutOffer, setCashOutOffer] = useState<CashOutOffer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Simulate real-time cash out value updates
  useEffect(() => {
    if (!bet.isLive || bet.status !== 'pending') return;

    const updateCashOutValue = () => {
      // Simulate cash out value calculation based on current odds and time
      const timeElapsed = Date.now() - bet.placedAt.getTime();
      const baseValue = bet.amount * 0.8; // Base 80% of stake
      const variance = (Math.random() - 0.5) * 0.3; // ±15% variance
      const timeDecay = Math.max(0.5, 1 - (timeElapsed / (1000 * 60 * 90))); // Decay over 90 minutes
      
      const cashOutValue = Math.max(
        bet.amount * 0.1, // Minimum 10% of stake
        Math.min(
          bet.potentialWin * 0.95, // Maximum 95% of potential win
          baseValue * (1 + variance) * timeDecay
        )
      );

      const percentage = (cashOutValue / bet.amount) * 100;

      setCashOutOffer({
        betId: bet.id,
        amount: Math.round(cashOutValue * 100) / 100,
        percentage: Math.round(percentage * 100) / 100,
        expiresAt: new Date(Date.now() + 30000), // Expires in 30 seconds
      });
    };

    updateCashOutValue();
    const interval = setInterval(updateCashOutValue, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [bet]);

  const handleCashOut = async () => {
    if (!cashOutOffer) return;

    setIsLoading(true);
    try {
      await onCashOut(bet.id, cashOutOffer.amount);
      setShowConfirmation(false);
    } catch (error) {
      console.error('Cash out failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = () => {
    if (!cashOutOffer) return 'text-gray-400';
    
    if (cashOutOffer.percentage > 100) return 'text-green-400';
    if (cashOutOffer.percentage > 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getStatusIcon = () => {
    if (!cashOutOffer) return <Clock className="w-4 h-4" />;
    
    if (cashOutOffer.percentage > 100) return <TrendingUp className="w-4 h-4" />;
    if (cashOutOffer.percentage > 80) return <DollarSign className="w-4 h-4" />;
    return <TrendingDown className="w-4 h-4" />;
  };

  // Don't show cash out for non-live bets or settled bets
  if (!bet.isLive || bet.status !== 'pending') {
    return null;
  }

  return (
    <>
      <Card glass className="p-4 border-l-4 border-blue-500">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
            <span className="text-white font-medium">Live Bet</span>
          </div>
          <div className="flex items-center space-x-1 text-blue-400">
            <DollarSign className="w-4 h-4" />
            <span className="text-sm">Cash Out Available</span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-white/60">Original Stake</span>
            <span className="text-white font-semibold">€{bet.amount.toFixed(2)}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-white/60">Potential Win</span>
            <span className="text-green-400 font-semibold">€{bet.potentialWin.toFixed(2)}</span>
          </div>

          {cashOutOffer && (
            <>
              <div className="border-t border-white/20 pt-3">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-white/60">Cash Out Value</span>
                    {getStatusIcon()}
                  </div>
                  <div className="text-right">
                    <div className={`font-bold text-lg ${getStatusColor()}`}>
                      €{cashOutOffer.amount.toFixed(2)}
                    </div>
                    <div className={`text-sm ${getStatusColor()}`}>
                      {cashOutOffer.percentage > 100 ? '+' : ''}
                      {(cashOutOffer.percentage - 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs text-white/60 mb-3">
                  <span>Offer expires in</span>
                  <CountdownTimer expiresAt={cashOutOffer.expiresAt} />
                </div>

                <Button
                  fullWidth
                  variant={cashOutOffer.percentage > 100 ? 'primary' : 'outline'}
                  className={
                    cashOutOffer.percentage <= 100
                      ? 'border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/20'
                      : ''
                  }
                  onClick={() => setShowConfirmation(true)}
                  leftIcon={<DollarSign className="w-4 h-4" />}
                >
                  Cash Out €{cashOutOffer.amount.toFixed(2)}
                </Button>
              </div>
            </>
          )}

          {!cashOutOffer && (
            <div className="flex items-center justify-center py-4 text-white/60">
              <Clock className="w-4 h-4 mr-2" />
              <span className="text-sm">Calculating cash out value...</span>
            </div>
          )}
        </div>
      </Card>

      {/* Confirmation Modal */}
      <AnimatePresence>
        {showConfirmation && cashOutOffer && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-slate-800 rounded-xl p-6 max-w-md w-full"
            >
              <div className="text-center mb-6">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 ${
                  cashOutOffer.percentage > 100 
                    ? 'bg-green-500/20' 
                    : 'bg-yellow-500/20'
                }`}>
                  {cashOutOffer.percentage > 100 ? (
                    <TrendingUp className="w-6 h-6 text-green-400" />
                  ) : (
                    <AlertCircle className="w-6 h-6 text-yellow-400" />
                  )}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Confirm Cash Out
                </h3>
                <p className="text-white/60">
                  You will receive €{cashOutOffer.amount.toFixed(2)} immediately
                </p>
              </div>

              <div className="bg-white/5 rounded-lg p-4 mb-6">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/60">Original Stake</span>
                    <span className="text-white">€{bet.amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Cash Out Amount</span>
                    <span className={`font-semibold ${getStatusColor()}`}>
                      €{cashOutOffer.amount.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Profit/Loss</span>
                    <span className={`font-semibold ${
                      cashOutOffer.amount > bet.amount ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {cashOutOffer.amount > bet.amount ? '+' : ''}
                      €{(cashOutOffer.amount - bet.amount).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  fullWidth
                  onClick={() => setShowConfirmation(false)}
                  className="border-white/30 text-white hover:bg-white/10"
                >
                  Cancel
                </Button>
                <Button
                  fullWidth
                  onClick={handleCashOut}
                  isLoading={isLoading}
                  variant={cashOutOffer.percentage > 100 ? 'primary' : 'outline'}
                  className={
                    cashOutOffer.percentage <= 100
                      ? 'border-yellow-500 text-yellow-400 hover:bg-yellow-500/20'
                      : ''
                  }
                >
                  Confirm Cash Out
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Countdown timer component
const CountdownTimer = ({ expiresAt }: { expiresAt: Date }) => {
  const [timeLeft, setTimeLeft] = useState(0);

  useEffect(() => {
    const updateTimer = () => {
      const now = Date.now();
      const timeRemaining = Math.max(0, expiresAt.getTime() - now);
      setTimeLeft(Math.ceil(timeRemaining / 1000));
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [expiresAt]);

  return (
    <span className={`font-mono ${timeLeft <= 10 ? 'text-red-400' : 'text-white/60'}`}>
      {timeLeft}s
    </span>
  );
};

export default CashOut;
