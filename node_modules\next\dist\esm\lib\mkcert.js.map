{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "names": ["fs", "path", "getCacheDirectory", "Log", "execSync", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "binaryPath", "join", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "info", "response", "fetch", "ok", "body", "status", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "from", "writeFile", "chmod", "err", "error", "createSelfSignedCertificate", "host", "certDir", "resolvedCertDir", "resolve", "cwd", "keyP<PERSON>", "certPath", "defaultHosts", "hosts", "includes", "stdio", "caLocation", "toString", "trim", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile", "key", "cert", "rootCA"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,gBAAe;AAExC,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,KAAK,CAAC;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,KAAK,CAAC;IACjD;IAEA,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,SAAS,CAAC;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBX,kBAAkB;QACzC,MAAMY,aAAab,KAAKc,IAAI,CAACF,gBAAgBD;QAE7C,IAAIZ,GAAGgB,UAAU,CAACF,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMG,cAAc,CAAC,wDAAwD,EAAEZ,eAAe,CAAC,EAAEO,WAAW,CAAC;QAE7G,MAAMZ,GAAGkB,QAAQ,CAACC,KAAK,CAACN,gBAAgB;YAAEO,WAAW;QAAK;QAE1DjB,IAAIkB,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMC,MAAMN;QAE7B,IAAI,CAACK,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAClC,MAAM,IAAIf,MAAM,CAAC,2BAA2B,EAAEY,SAASI,MAAM,CAAC,CAAC;QACjE;QAEAvB,IAAIkB,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMM,cAAc,MAAML,SAASK,WAAW;QAC9C,MAAMC,SAASC,OAAOC,IAAI,CAACH;QAE3B,MAAM3B,GAAGkB,QAAQ,CAACa,SAAS,CAACjB,YAAYc;QACxC,MAAM5B,GAAGkB,QAAQ,CAACc,KAAK,CAAClB,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAOmB,KAAK;QACZ9B,IAAI+B,KAAK,CAAC,6BAA6BD;IACzC;AACF;AAEA,OAAO,eAAeE,4BACpBC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAMvB,aAAa,MAAMH;QACzB,IAAI,CAACG,YAAY,MAAM,IAAIJ,MAAM;QAEjC,MAAM4B,kBAAkBrC,KAAKsC,OAAO,CAAC/B,QAAQgC,GAAG,IAAI,CAAC,EAAE,EAAEH,QAAQ,CAAC;QAElE,MAAMrC,GAAGkB,QAAQ,CAACC,KAAK,CAACmB,iBAAiB;YACvClB,WAAW;QACb;QAEA,MAAMqB,UAAUxC,KAAKsC,OAAO,CAACD,iBAAiB;QAC9C,MAAMI,WAAWzC,KAAKsC,OAAO,CAACD,iBAAiB;QAE/CnC,IAAIkB,IAAI,CACN;QAGF,MAAMsB,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJR,QAAQ,CAACO,aAAaE,QAAQ,CAACT,QAC3B;eAAIO;YAAcP;SAAK,GACvBO;QAENvC,SACE,CAAC,CAAC,EAAEU,WAAW,sBAAsB,EAAE2B,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEE,MAAM7B,IAAI,CACpF,KACA,CAAC,EACH;YAAE+B,OAAO;QAAS;QAGpB,MAAMC,aAAa3C,SAAS,CAAC,CAAC,EAAEU,WAAW,SAAS,CAAC,EAAEkC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAACjD,GAAGgB,UAAU,CAACyB,YAAY,CAACzC,GAAGgB,UAAU,CAAC0B,WAAW;YACvD,MAAM,IAAIhC,MAAM;QAClB;QAEAP,IAAIkB,IAAI,CAAC,CAAC,+BAA+B,EAAE0B,WAAW,CAAC;QACvD5C,IAAIkB,IAAI,CAAC,CAAC,wBAAwB,EAAEiB,gBAAgB,CAAC;QAErD,MAAMY,gBAAgBjD,KAAKsC,OAAO,CAAC/B,QAAQgC,GAAG,IAAI;QAElD,IAAIxC,GAAGgB,UAAU,CAACkC,gBAAgB;YAChC,MAAMC,YAAY,MAAMnD,GAAGkB,QAAQ,CAACkC,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUN,QAAQ,CAACR,UAAU;gBAChClC,IAAIkB,IAAI,CAAC;gBAET,MAAMrB,GAAGkB,QAAQ,CAACmC,UAAU,CAACH,eAAe,CAAC,EAAE,EAAEb,QAAQ,CAAC;YAC5D;QACF;QAEA,OAAO;YACLiB,KAAKb;YACLc,MAAMb;YACNc,QAAQ,CAAC,EAAET,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOd,KAAK;QACZ9B,IAAI+B,KAAK,CACP,qEACAD;IAEJ;AACF"}