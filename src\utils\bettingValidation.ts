import { BetSlipItem, BettingLimits, BetValidationError } from '@/types';

export const validateBetAmount = (
  amount: number,
  limits: BettingLimits
): BetValidationError[] => {
  const errors: BetValidationError[] = [];

  if (amount <= 0) {
    errors.push({
      field: 'amount',
      message: 'Bet amount must be greater than 0',
      code: 'INVALID_AMOUNT',
    });
  }

  if (amount < limits.minBet) {
    errors.push({
      field: 'amount',
      message: `Minimum bet amount is €${limits.minBet}`,
      code: 'BELOW_MIN_BET',
    });
  }

  if (amount > limits.maxBet) {
    errors.push({
      field: 'amount',
      message: `Maximum bet amount is €${limits.maxBet}`,
      code: 'ABOVE_MAX_BET',
    });
  }

  return errors;
};

export const validateBetSlip = (
  items: BetSlipItem[],
  userBalance: number,
  limits: BettingLimits,
  betType: 'single' | 'parlay'
): BetValidationError[] => {
  const errors: BetValidationError[] = [];

  if (items.length === 0) {
    errors.push({
      field: 'betSlip',
      message: 'No bets selected',
      code: 'EMPTY_BET_SLIP',
    });
    return errors;
  }

  // Validate individual bet amounts
  items.forEach((item, index) => {
    const amountErrors = validateBetAmount(item.amount, limits);
    amountErrors.forEach(error => {
      errors.push({
        ...error,
        field: `items[${index}].amount`,
      });
    });
  });

  // Validate total stake vs balance
  const totalStake = items.reduce((sum, item) => sum + item.amount, 0);
  if (totalStake > userBalance) {
    errors.push({
      field: 'totalStake',
      message: `Insufficient balance. You have €${userBalance.toFixed(2)} but need €${totalStake.toFixed(2)}`,
      code: 'INSUFFICIENT_BALANCE',
    });
  }

  // Validate parlay constraints
  if (betType === 'parlay') {
    if (items.length < 2) {
      errors.push({
        field: 'betType',
        message: 'Parlay bets require at least 2 selections',
        code: 'INSUFFICIENT_PARLAY_LEGS',
      });
    }

    if (items.length > limits.maxParlayLegs) {
      errors.push({
        field: 'betType',
        message: `Maximum ${limits.maxParlayLegs} selections allowed in parlay`,
        code: 'TOO_MANY_PARLAY_LEGS',
      });
    }

    // Check for conflicting selections (same match)
    const matchIds = items.map(item => item.matchId);
    const uniqueMatchIds = new Set(matchIds);
    if (matchIds.length !== uniqueMatchIds.size) {
      errors.push({
        field: 'selections',
        message: 'Cannot have multiple selections from the same match in a parlay',
        code: 'CONFLICTING_SELECTIONS',
      });
    }
  }

  // Validate potential payout
  const totalPotentialWin = betType === 'parlay' 
    ? totalStake * items.reduce((odds, item) => odds * item.selection.odds, 1)
    : items.reduce((sum, item) => sum + item.potentialWin, 0);

  if (totalPotentialWin > limits.maxPayout) {
    errors.push({
      field: 'potentialWin',
      message: `Maximum payout is €${limits.maxPayout}. Current potential win: €${totalPotentialWin.toFixed(2)}`,
      code: 'EXCEEDS_MAX_PAYOUT',
    });
  }

  return errors;
};

export const validateOddsChange = (
  originalOdds: number,
  currentOdds: number,
  tolerance = 0.1
): boolean => {
  const change = Math.abs(originalOdds - currentOdds);
  const percentageChange = change / originalOdds;
  return percentageChange <= tolerance;
};

export const formatValidationErrors = (errors: BetValidationError[]): string[] => {
  return errors.map(error => error.message);
};

export const groupValidationErrors = (errors: BetValidationError[]) => {
  const grouped: { [key: string]: BetValidationError[] } = {};
  
  errors.forEach(error => {
    const field = error.field.split('[')[0]; // Group array fields together
    if (!grouped[field]) {
      grouped[field] = [];
    }
    grouped[field].push(error);
  });

  return grouped;
};

// Responsible gambling validation
export const validateResponsibleGambling = (
  amount: number,
  dailySpent: number,
  weeklySpent: number,
  monthlySpent: number,
  limits: {
    daily?: number;
    weekly?: number;
    monthly?: number;
  }
): BetValidationError[] => {
  const errors: BetValidationError[] = [];

  if (limits.daily && dailySpent + amount > limits.daily) {
    errors.push({
      field: 'dailyLimit',
      message: `This bet would exceed your daily spending limit of €${limits.daily}`,
      code: 'EXCEEDS_DAILY_LIMIT',
    });
  }

  if (limits.weekly && weeklySpent + amount > limits.weekly) {
    errors.push({
      field: 'weeklyLimit',
      message: `This bet would exceed your weekly spending limit of €${limits.weekly}`,
      code: 'EXCEEDS_WEEKLY_LIMIT',
    });
  }

  if (limits.monthly && monthlySpent + amount > limits.monthly) {
    errors.push({
      field: 'monthlyLimit',
      message: `This bet would exceed your monthly spending limit of €${limits.monthly}`,
      code: 'EXCEEDS_MONTHLY_LIMIT',
    });
  }

  return errors;
};

// Betting pattern analysis
export const analyzeBettingPattern = (
  recentBets: { amount: number; timestamp: Date; status: string }[],
  timeWindow = 24 * 60 * 60 * 1000 // 24 hours
): {
  isRiskyPattern: boolean;
  warnings: string[];
  recommendations: string[];
} => {
  const now = Date.now();
  const recentBetsInWindow = recentBets.filter(
    bet => now - bet.timestamp.getTime() <= timeWindow
  );

  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Check for rapid betting
  if (recentBetsInWindow.length > 10) {
    warnings.push('You have placed many bets recently');
    recommendations.push('Consider taking a break from betting');
  }

  // Check for increasing bet amounts
  const amounts = recentBetsInWindow.map(bet => bet.amount);
  if (amounts.length >= 3) {
    const isIncreasing = amounts.slice(-3).every((amount, index, arr) => 
      index === 0 || amount > arr[index - 1]
    );
    
    if (isIncreasing) {
      warnings.push('Your bet amounts have been increasing');
      recommendations.push('Consider setting a maximum bet limit');
    }
  }

  // Check for chasing losses
  const lostBets = recentBetsInWindow.filter(bet => bet.status === 'lost');
  if (lostBets.length >= 3) {
    const lastThreeBets = recentBetsInWindow.slice(-3);
    const allLosses = lastThreeBets.every(bet => bet.status === 'lost');
    
    if (allLosses) {
      warnings.push('You have had several losses in a row');
      recommendations.push('Consider taking a break to avoid chasing losses');
    }
  }

  return {
    isRiskyPattern: warnings.length > 0,
    warnings,
    recommendations,
  };
};
