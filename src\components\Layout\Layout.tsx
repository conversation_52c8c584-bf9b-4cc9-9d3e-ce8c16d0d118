'use client';

import { ReactNode } from 'react';
import { AnimatePresence } from 'framer-motion';
import Header from './Header';
import Footer from './Footer';
import { BettingProvider } from '@/contexts/BettingContext';
import BetSlip from '@/components/Betting/BetSlip';
import BetSlipToggle from '@/components/Betting/BetSlipToggle';

interface LayoutProps {
  children: ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  showBetting?: boolean;
  className?: string;
}

const Layout = ({
  children,
  showHeader = true,
  showFooter = true,
  showBetting = true,
  className = ''
}: LayoutProps) => {
  return (
    <BettingProvider>
      <div className={`min-h-screen flex flex-col ${className}`}>
        {showHeader && <Header />}

        <main className={`flex-1 ${showHeader ? 'pt-16' : ''}`}>
          {children}
        </main>

        {showFooter && <Footer />}

        {/* Betting Components */}
        {showBetting && (
          <>
            <AnimatePresence>
              <BetSlip />
            </AnimatePresence>
            <BetSlipToggle />
          </>
        )}
      </div>
    </BettingProvider>
  );
};

export default Layout;
