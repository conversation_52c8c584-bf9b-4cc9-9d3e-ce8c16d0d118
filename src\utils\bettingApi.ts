import { Bet, BetSlipItem, OddsUpdate, CashOutOffer } from '@/types';

// Mock API functions for betting system

export const bettingApi = {
  // Place bets
  placeBets: async (betSlipItems: BetSlipItem[]): Promise<Bet[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

    // Simulate occasional failures
    if (Math.random() < 0.05) {
      throw new Error('Network error. Please try again.');
    }

    // Create bet objects
    const bets: Bet[] = betSlipItems.map(item => ({
      id: Math.random().toString(36).substr(2, 9),
      userId: 'user-1',
      matchId: item.matchId,
      type: item.selection.type,
      amount: item.amount,
      odds: item.selection.odds,
      potentialWin: item.potentialWin,
      status: 'pending' as const,
      placedAt: new Date(),
      isLive: item.match.isLive,
    }));

    return bets;
  },

  // Cash out bet
  cashOutBet: async (betId: string, amount: number): Promise<{ success: boolean; newBalance: number }> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 500));

    // Simulate occasional failures
    if (Math.random() < 0.1) {
      throw new Error('Cash out failed. Odds may have changed.');
    }

    return {
      success: true,
      newBalance: 1250.75 + amount, // Mock new balance
    };
  },

  // Get live odds updates
  subscribeToOddsUpdates: (callback: (update: OddsUpdate) => void) => {
    const interval = setInterval(() => {
      // Simulate random odds updates
      const matches = ['1', '2', '3', '4'];
      const selectionTypes = ['home', 'away', 'draw', 'over', 'under'];
      
      const randomMatch = matches[Math.floor(Math.random() * matches.length)];
      const randomSelection = selectionTypes[Math.floor(Math.random() * selectionTypes.length)];
      
      const baseOdds = 1.5 + Math.random() * 3; // Random odds between 1.5 and 4.5
      const change = (Math.random() - 0.5) * 0.3; // ±0.15 change
      const newOdds = Math.max(1.01, baseOdds + change);

      const update: OddsUpdate = {
        matchId: randomMatch,
        marketType: 'match_result',
        selectionType: randomSelection,
        newOdds: Math.round(newOdds * 100) / 100,
        timestamp: new Date(),
      };

      callback(update);
    }, 3000 + Math.random() * 7000); // Random interval between 3-10 seconds

    return () => clearInterval(interval);
  },

  // Get cash out offer
  getCashOutOffer: async (betId: string): Promise<CashOutOffer | null> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 500));

    // Simulate no cash out available sometimes
    if (Math.random() < 0.2) {
      return null;
    }

    // Mock cash out calculation
    const baseAmount = 50 + Math.random() * 200; // Random amount between 50-250
    const percentage = 70 + Math.random() * 60; // Random percentage between 70-130%

    return {
      betId,
      amount: Math.round(baseAmount * 100) / 100,
      percentage: Math.round(percentage * 100) / 100,
      expiresAt: new Date(Date.now() + 30000), // Expires in 30 seconds
    };
  },

  // Validate bet placement
  validateBet: (betSlipItem: BetSlipItem, userBalance: number, limits: any) => {
    const errors: string[] = [];

    if (betSlipItem.amount < limits.minBet) {
      errors.push(`Minimum bet amount is €${limits.minBet}`);
    }

    if (betSlipItem.amount > limits.maxBet) {
      errors.push(`Maximum bet amount is €${limits.maxBet}`);
    }

    if (betSlipItem.amount > userBalance) {
      errors.push('Insufficient balance');
    }

    if (betSlipItem.potentialWin > limits.maxPayout) {
      errors.push(`Maximum payout is €${limits.maxPayout}`);
    }

    // Check if odds have changed significantly
    if (Math.random() < 0.1) {
      errors.push('Odds have changed. Please review your selection.');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Get betting limits
  getBettingLimits: async () => {
    await new Promise(resolve => setTimeout(resolve, 200));

    return {
      minBet: 1,
      maxBet: 1000,
      maxPayout: 10000,
      maxParlayLegs: 10,
    };
  },

  // Get user betting history
  getBettingHistory: async (userId: string, limit = 20) => {
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock betting history
    const history: Bet[] = [
      {
        id: 'bet-1',
        userId,
        matchId: '1',
        type: 'home',
        amount: 50,
        odds: 2.45,
        potentialWin: 122.50,
        status: 'won',
        placedAt: new Date('2024-01-14T15:30:00Z'),
        settledAt: new Date('2024-01-14T17:00:00Z'),
        isLive: false,
      },
      {
        id: 'bet-2',
        userId,
        matchId: '2',
        type: 'over',
        amount: 75,
        odds: 1.90,
        potentialWin: 142.50,
        status: 'pending',
        placedAt: new Date('2024-01-15T14:00:00Z'),
        isLive: true,
      },
      {
        id: 'bet-3',
        userId,
        matchId: '3',
        type: 'away',
        amount: 100,
        odds: 1.85,
        potentialWin: 185.00,
        status: 'lost',
        placedAt: new Date('2024-01-13T16:45:00Z'),
        settledAt: new Date('2024-01-13T18:30:00Z'),
        isLive: false,
      },
    ];

    return history.slice(0, limit);
  },

  // Update user balance
  updateBalance: async (userId: string, amount: number) => {
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock balance update
    return {
      success: true,
      newBalance: 1250.75 + amount,
      transaction: {
        id: Math.random().toString(36).substr(2, 9),
        type: amount > 0 ? 'credit' : 'debit',
        amount: Math.abs(amount),
        timestamp: new Date(),
      },
    };
  },
};

// Responsible gambling utilities
export const responsibleGambling = {
  // Check daily/weekly/monthly limits
  checkLimits: async (userId: string, amount: number) => {
    await new Promise(resolve => setTimeout(resolve, 200));

    // Mock limit checking
    const dailySpent = 150; // Mock daily spending
    const dailyLimit = 500;
    const weeklySpent = 800;
    const weeklyLimit = 2000;

    const warnings: string[] = [];

    if (dailySpent + amount > dailyLimit) {
      warnings.push(`This bet would exceed your daily limit of €${dailyLimit}`);
    }

    if (weeklySpent + amount > weeklyLimit) {
      warnings.push(`This bet would exceed your weekly limit of €${weeklyLimit}`);
    }

    return {
      canProceed: warnings.length === 0,
      warnings,
      limits: {
        daily: { spent: dailySpent, limit: dailyLimit },
        weekly: { spent: weeklySpent, limit: weeklyLimit },
      },
    };
  },

  // Get self-exclusion status
  getSelfExclusionStatus: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 100));

    return {
      isExcluded: false,
      excludedUntil: null,
      canSetExclusion: true,
    };
  },

  // Set betting limits
  setBettingLimits: async (userId: string, limits: any) => {
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      effectiveDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
    };
  },
};
