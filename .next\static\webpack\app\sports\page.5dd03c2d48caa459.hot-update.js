"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sports/page",{

/***/ "(app-pages-browser)/./src/components/Betting/BetSlip.tsx":
/*!********************************************!*\
  !*** ./src/components/Betting/BetSlip.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(app-pages-browser)/./src/contexts/BettingContext.tsx\");\n/* harmony import */ var _components_UI_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Button */ \"(app-pages-browser)/./src/components/UI/Button.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/UI/Card */ \"(app-pages-browser)/./src/components/UI/Card.tsx\");\n/* harmony import */ var _utils_bettingValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/bettingValidation */ \"(app-pages-browser)/./src/utils/bettingValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BetSlip = ()=>{\n    _s();\n    const { state, removeFromBetSlip, updateBetAmount, clearBetSlip, setBetType, placeBets, toggleBetSlip } = (0,_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const [showConfirmation, setShowConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [acceptedTerms, setAcceptedTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const quickAmounts = [\n        10,\n        25,\n        50,\n        100\n    ];\n    const handleQuickAmount = (itemId, amount)=>{\n        updateBetAmount(itemId, amount);\n    };\n    const handleAmountChange = (itemId, value)=>{\n        const amount = parseFloat(value) || 0;\n        if (amount >= 0 && amount <= state.bettingLimits.maxBet) {\n            updateBetAmount(itemId, amount);\n        }\n    };\n    const getValidationErrors = ()=>{\n        return (0,_utils_bettingValidation__WEBPACK_IMPORTED_MODULE_5__.validateBetSlip)(state.betSlip.items, state.userBalance, state.bettingLimits, state.betSlip.betType);\n    };\n    const canPlaceBets = ()=>{\n        const errors = getValidationErrors();\n        return errors.length === 0 && acceptedTerms;\n    };\n    const handlePlaceBets = async ()=>{\n        if (!acceptedTerms) {\n            setShowConfirmation(true);\n            return;\n        }\n        await placeBets();\n    };\n    const confirmPlaceBets = async ()=>{\n        setShowConfirmation(false);\n        await placeBets();\n    };\n    if (!state.isSlipOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\",\n                onClick: toggleBetSlip\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    x: \"100%\"\n                },\n                animate: {\n                    x: 0\n                },\n                exit: {\n                    x: \"100%\"\n                },\n                transition: {\n                    type: \"spring\",\n                    damping: 25,\n                    stiffness: 200\n                },\n                className: \"fixed right-0 top-0 h-full w-full max-w-md bg-slate-900 border-l border-white/20 z-50 overflow-hidden flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-white/20 bg-slate-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Bet Slip\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            state.betSlip.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearBetSlip,\n                                                className: \"p-2 text-white/60 hover:text-red-400 transition-colors\",\n                                                title: \"Clear all bets\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleBetSlip,\n                                                className: \"p-2 text-white/60 hover:text-white transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            state.betSlip.items.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 flex bg-white/10 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setBetType(\"single\"),\n                                        className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all \".concat(state.betSlip.betType === \"single\" ? \"bg-blue-600 text-white\" : \"text-white/80 hover:text-white\"),\n                                        children: \"Single Bets\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setBetType(\"parlay\"),\n                                        className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all \".concat(state.betSlip.betType === \"parlay\" ? \"bg-blue-600 text-white\" : \"text-white/80 hover:text-white\"),\n                                        children: \"Parlay\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: state.betSlip.items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-12 h-12 text-white/40 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-2\",\n                                    children: \"Your bet slip is empty\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/40 text-sm\",\n                                    children: \"Click on odds to add selections to your bet slip\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: state.betSlip.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        glass: true,\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400 text-xs font-medium mb-1\",\n                                                                children: item.match.league\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium text-sm mb-1\",\n                                                                children: [\n                                                                    item.match.homeTeam,\n                                                                    \" vs \",\n                                                                    item.match.awayTeam\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/80 text-sm\",\n                                                                children: item.selection.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeFromBetSlip(item.id),\n                                                        className: \"p-1 text-white/60 hover:text-red-400 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: \"Odds\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: item.selection.odds.toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-1 mb-3\",\n                                                children: quickAmounts.map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickAmount(item.id, amount),\n                                                        className: \"py-1 px-2 bg-white/10 hover:bg-white/20 text-white text-xs rounded transition-colors\",\n                                                        children: [\n                                                            \"€\",\n                                                            amount\n                                                        ]\n                                                    }, amount, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white/60 text-xs mb-1\",\n                                                        children: [\n                                                            \"Stake (€\",\n                                                            state.bettingLimits.minBet,\n                                                            \" - €\",\n                                                            state.bettingLimits.maxBet,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        min: state.bettingLimits.minBet,\n                                                        max: state.bettingLimits.maxBet,\n                                                        step: \"0.01\",\n                                                        value: item.amount || \"\",\n                                                        onChange: (e)=>handleAmountChange(item.id, e.target.value),\n                                                        className: \"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-blue-500\",\n                                                        placeholder: \"Enter amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Potential Win\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 font-semibold\",\n                                                        children: [\n                                                            \"€\",\n                                                            item.potentialWin.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.id, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    state.betSlip.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-white/20 bg-slate-800/50\",\n                        children: [\n                            state.betSlip.betType === \"parlay\" && state.betSlip.parlayOdds && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-purple-500/20 border border-purple-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-300\",\n                                            children: \"Parlay Odds\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-300 font-semibold\",\n                                            children: state.betSlip.parlayOdds.toFixed(2)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"Total Stake\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    \"€\",\n                                                    state.betSlip.totalStake.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"Potential Win\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-semibold\",\n                                                children: [\n                                                    \"€\",\n                                                    state.betSlip.totalPotentialWin.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"Your Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: [\n                                                    \"€\",\n                                                    state.userBalance.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined),\n                            state.betSlip.totalStake > state.userBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 p-2 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-300 text-sm\",\n                                        children: \"Insufficient balance\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined),\n                            state.betSlip.totalPotentialWin > state.bettingLimits.maxPayout && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 p-2 bg-yellow-500/20 border border-yellow-500/30 rounded-lg flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-300 text-sm\",\n                                        children: [\n                                            \"Exceeds maximum payout (€\",\n                                            state.bettingLimits.maxPayout,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fullWidth: true,\n                                size: \"lg\",\n                                disabled: !canPlaceBets(),\n                                isLoading: state.isLoading,\n                                onClick: handlePlaceBets,\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 25\n                                }, void 0),\n                                children: [\n                                    \"Place Bet\",\n                                    state.betSlip.items.length > 1 ? \"s\" : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-start space-x-2 mt-3 text-xs text-white/60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: acceptedTerms,\n                                        onChange: (e)=>setAcceptedTerms(e.target.checked),\n                                        className: \"w-3 h-3 mt-0.5 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"I accept the\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/terms\",\n                                                className: \"text-blue-400 hover:text-blue-300\",\n                                                children: \"terms and conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: showConfirmation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-slate-800 rounded-xl p-6 max-w-md w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: [\n                                            \"Confirm Your Bet\",\n                                            state.betSlip.items.length > 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60\",\n                                        children: [\n                                            \"You are about to place \",\n                                            state.betSlip.items.length,\n                                            \" bet\",\n                                            state.betSlip.items.length > 1 ? \"s\" : \"\",\n                                            \"for a total stake of €\",\n                                            state.betSlip.totalStake.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: acceptedTerms,\n                                            onChange: (e)=>setAcceptedTerms(e.target.checked),\n                                            className: \"w-4 h-4 mt-0.5 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: \"I confirm that I am 18+ and accept the terms and conditions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"outline\",\n                                        fullWidth: true,\n                                        onClick: ()=>setShowConfirmation(false),\n                                        className: \"border-white/30 text-white hover:bg-white/10\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        fullWidth: true,\n                                        disabled: !acceptedTerms,\n                                        onClick: confirmPlaceBets,\n                                        isLoading: state.isLoading,\n                                        children: \"Confirm Bet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(BetSlip, \"HhlASE/YkHvuxYeOhSGQ/sYjgfo=\", false, function() {\n    return [\n        _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__.useBetting\n    ];\n});\n_c = BetSlip;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BetSlip);\nvar _c;\n$RefreshReg$(_c, \"BetSlip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Betting/BetSlip.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/bettingValidation.ts":
/*!****************************************!*\
  !*** ./src/utils/bettingValidation.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeBettingPattern: function() { return /* binding */ analyzeBettingPattern; },\n/* harmony export */   formatValidationErrors: function() { return /* binding */ formatValidationErrors; },\n/* harmony export */   groupValidationErrors: function() { return /* binding */ groupValidationErrors; },\n/* harmony export */   validateBetAmount: function() { return /* binding */ validateBetAmount; },\n/* harmony export */   validateBetSlip: function() { return /* binding */ validateBetSlip; },\n/* harmony export */   validateOddsChange: function() { return /* binding */ validateOddsChange; },\n/* harmony export */   validateResponsibleGambling: function() { return /* binding */ validateResponsibleGambling; }\n/* harmony export */ });\nconst validateBetAmount = (amount, limits)=>{\n    const errors = [];\n    if (amount <= 0) {\n        errors.push({\n            field: \"amount\",\n            message: \"Bet amount must be greater than 0\",\n            code: \"INVALID_AMOUNT\"\n        });\n    }\n    if (amount < limits.minBet) {\n        errors.push({\n            field: \"amount\",\n            message: \"Minimum bet amount is €\".concat(limits.minBet),\n            code: \"BELOW_MIN_BET\"\n        });\n    }\n    if (amount > limits.maxBet) {\n        errors.push({\n            field: \"amount\",\n            message: \"Maximum bet amount is €\".concat(limits.maxBet),\n            code: \"ABOVE_MAX_BET\"\n        });\n    }\n    return errors;\n};\nconst validateBetSlip = (items, userBalance, limits, betType)=>{\n    const errors = [];\n    if (items.length === 0) {\n        errors.push({\n            field: \"betSlip\",\n            message: \"No bets selected\",\n            code: \"EMPTY_BET_SLIP\"\n        });\n        return errors;\n    }\n    // Validate individual bet amounts\n    items.forEach((item, index)=>{\n        const amountErrors = validateBetAmount(item.amount, limits);\n        amountErrors.forEach((error)=>{\n            errors.push({\n                ...error,\n                field: \"items[\".concat(index, \"].amount\")\n            });\n        });\n    });\n    // Validate total stake vs balance\n    const totalStake = items.reduce((sum, item)=>sum + item.amount, 0);\n    if (totalStake > userBalance) {\n        errors.push({\n            field: \"totalStake\",\n            message: \"Insufficient balance. You have €\".concat(userBalance.toFixed(2), \" but need €\").concat(totalStake.toFixed(2)),\n            code: \"INSUFFICIENT_BALANCE\"\n        });\n    }\n    // Validate parlay constraints\n    if (betType === \"parlay\") {\n        if (items.length < 2) {\n            errors.push({\n                field: \"betType\",\n                message: \"Parlay bets require at least 2 selections\",\n                code: \"INSUFFICIENT_PARLAY_LEGS\"\n            });\n        }\n        if (items.length > limits.maxParlayLegs) {\n            errors.push({\n                field: \"betType\",\n                message: \"Maximum \".concat(limits.maxParlayLegs, \" selections allowed in parlay\"),\n                code: \"TOO_MANY_PARLAY_LEGS\"\n            });\n        }\n        // Check for conflicting selections (same match)\n        const matchIds = items.map((item)=>item.matchId);\n        const uniqueMatchIds = new Set(matchIds);\n        if (matchIds.length !== uniqueMatchIds.size) {\n            errors.push({\n                field: \"selections\",\n                message: \"Cannot have multiple selections from the same match in a parlay\",\n                code: \"CONFLICTING_SELECTIONS\"\n            });\n        }\n    }\n    // Validate potential payout\n    const totalPotentialWin = betType === \"parlay\" ? totalStake * items.reduce((odds, item)=>odds * item.selection.odds, 1) : items.reduce((sum, item)=>sum + item.potentialWin, 0);\n    if (totalPotentialWin > limits.maxPayout) {\n        errors.push({\n            field: \"potentialWin\",\n            message: \"Maximum payout is €\".concat(limits.maxPayout, \". Current potential win: €\").concat(totalPotentialWin.toFixed(2)),\n            code: \"EXCEEDS_MAX_PAYOUT\"\n        });\n    }\n    return errors;\n};\nconst validateOddsChange = function(originalOdds, currentOdds) {\n    let tolerance = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0.1;\n    const change = Math.abs(originalOdds - currentOdds);\n    const percentageChange = change / originalOdds;\n    return percentageChange <= tolerance;\n};\nconst formatValidationErrors = (errors)=>{\n    return errors.map((error)=>error.message);\n};\nconst groupValidationErrors = (errors)=>{\n    const grouped = {};\n    errors.forEach((error)=>{\n        const field = error.field.split(\"[\")[0]; // Group array fields together\n        if (!grouped[field]) {\n            grouped[field] = [];\n        }\n        grouped[field].push(error);\n    });\n    return grouped;\n};\n// Responsible gambling validation\nconst validateResponsibleGambling = (amount, dailySpent, weeklySpent, monthlySpent, limits)=>{\n    const errors = [];\n    if (limits.daily && dailySpent + amount > limits.daily) {\n        errors.push({\n            field: \"dailyLimit\",\n            message: \"This bet would exceed your daily spending limit of €\".concat(limits.daily),\n            code: \"EXCEEDS_DAILY_LIMIT\"\n        });\n    }\n    if (limits.weekly && weeklySpent + amount > limits.weekly) {\n        errors.push({\n            field: \"weeklyLimit\",\n            message: \"This bet would exceed your weekly spending limit of €\".concat(limits.weekly),\n            code: \"EXCEEDS_WEEKLY_LIMIT\"\n        });\n    }\n    if (limits.monthly && monthlySpent + amount > limits.monthly) {\n        errors.push({\n            field: \"monthlyLimit\",\n            message: \"This bet would exceed your monthly spending limit of €\".concat(limits.monthly),\n            code: \"EXCEEDS_MONTHLY_LIMIT\"\n        });\n    }\n    return errors;\n};\n// Betting pattern analysis\nconst analyzeBettingPattern = function(recentBets) {\n    let timeWindow = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 24 * 60 * 60 * 1000 // 24 hours\n    ;\n    const now = Date.now();\n    const recentBetsInWindow = recentBets.filter((bet)=>now - bet.timestamp.getTime() <= timeWindow);\n    const warnings = [];\n    const recommendations = [];\n    // Check for rapid betting\n    if (recentBetsInWindow.length > 10) {\n        warnings.push(\"You have placed many bets recently\");\n        recommendations.push(\"Consider taking a break from betting\");\n    }\n    // Check for increasing bet amounts\n    const amounts = recentBetsInWindow.map((bet)=>bet.amount);\n    if (amounts.length >= 3) {\n        const isIncreasing = amounts.slice(-3).every((amount, index, arr)=>index === 0 || amount > arr[index - 1]);\n        if (isIncreasing) {\n            warnings.push(\"Your bet amounts have been increasing\");\n            recommendations.push(\"Consider setting a maximum bet limit\");\n        }\n    }\n    // Check for chasing losses\n    const lostBets = recentBetsInWindow.filter((bet)=>bet.status === \"lost\");\n    if (lostBets.length >= 3) {\n        const lastThreeBets = recentBetsInWindow.slice(-3);\n        const allLosses = lastThreeBets.every((bet)=>bet.status === \"lost\");\n        if (allLosses) {\n            warnings.push(\"You have had several losses in a row\");\n            recommendations.push(\"Consider taking a break to avoid chasing losses\");\n        }\n    }\n    return {\n        isRiskyPattern: warnings.length > 0,\n        warnings,\n        recommendations\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/bettingValidation.ts\n"));

/***/ })

});