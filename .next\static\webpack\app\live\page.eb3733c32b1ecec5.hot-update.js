"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/live/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronDown\", [\n  [\"path\", { d: \"m6 9 6 6 6-6\", key: \"qrunsl\" }]\n]);\n\n\n//# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsb0JBQW9CLGdFQUFnQjtBQUNwQyxhQUFhLGtDQUFrQztBQUMvQzs7QUFFa0M7QUFDbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLWRvd24uanM/YjlhMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbihcIkNoZXZyb25Eb3duXCIsIFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTYgOSA2IDYgNi02XCIsIGtleTogXCJxcnVuc2xcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IENoZXZyb25Eb3duIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZXZyb24tZG93bi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronUp\", [[\"path\", { d: \"m18 15-6-6-6 6\", key: \"153udz\" }]]);\n\n\n//# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGtCQUFrQixnRUFBZ0IsMEJBQTBCLG9DQUFvQzs7QUFFaEU7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLXVwLmpzP2QzNGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBDaGV2cm9uVXAgPSBjcmVhdGVMdWNpZGVJY29uKFwiQ2hldnJvblVwXCIsIFtbXCJwYXRoXCIsIHsgZDogXCJtMTggMTUtNi02LTYgNlwiLCBrZXk6IFwiMTUzdWR6XCIgfV1dKTtcblxuZXhwb3J0IHsgQ2hldnJvblVwIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZXZyb24tdXAuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/live/page.tsx":
/*!*******************************!*\
  !*** ./src/app/live/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LiveBettingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/Layout */ \"(app-pages-browser)/./src/components/Layout/Layout.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Card */ \"(app-pages-browser)/./src/components/UI/Card.tsx\");\n/* harmony import */ var _components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Betting/BetButton */ \"(app-pages-browser)/./src/components/Betting/BetButton.tsx\");\n/* harmony import */ var _components_Betting_BettingMarkets__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Betting/BettingMarkets */ \"(app-pages-browser)/./src/components/Betting/BettingMarkets.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LiveBettingPage() {\n    _s();\n    const [selectedMatch, setSelectedMatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    const liveMatches = [\n        {\n            id: 1,\n            sport: \"Football\",\n            league: \"Premier League\",\n            homeTeam: \"Manchester City\",\n            awayTeam: \"Arsenal\",\n            score: {\n                home: 2,\n                away: 1\n            },\n            time: \"67'\",\n            homeOdds: {\n                win: 1.45,\n                draw: 4.20,\n                lose: 6.50\n            },\n            awayOdds: {\n                win: 6.50,\n                draw: 4.20,\n                lose: 1.45\n            },\n            events: [\n                {\n                    time: \"65'\",\n                    type: \"goal\",\n                    team: \"home\",\n                    player: \"Haaland\"\n                },\n                {\n                    time: \"43'\",\n                    type: \"goal\",\n                    team: \"away\",\n                    player: \"Saka\"\n                },\n                {\n                    time: \"23'\",\n                    type: \"goal\",\n                    team: \"home\",\n                    player: \"De Bruyne\"\n                }\n            ],\n            stats: {\n                possession: {\n                    home: 58,\n                    away: 42\n                },\n                shots: {\n                    home: 12,\n                    away: 8\n                },\n                corners: {\n                    home: 6,\n                    away: 3\n                }\n            }\n        },\n        {\n            id: 2,\n            sport: \"Basketball\",\n            league: \"NBA\",\n            homeTeam: \"Lakers\",\n            awayTeam: \"Warriors\",\n            score: {\n                home: 89,\n                away: 94\n            },\n            time: \"Q3 8:45\",\n            homeOdds: {\n                win: 2.10,\n                spread: 1.90\n            },\n            awayOdds: {\n                win: 1.75,\n                spread: 1.90\n            },\n            events: [\n                {\n                    time: \"9:12\",\n                    type: \"score\",\n                    team: \"away\",\n                    player: \"Curry 3PT\"\n                },\n                {\n                    time: \"9:45\",\n                    type: \"score\",\n                    team: \"home\",\n                    player: \"LeBron 2PT\"\n                }\n            ],\n            stats: {\n                fieldGoal: {\n                    home: \"45%\",\n                    away: \"52%\"\n                },\n                threePoint: {\n                    home: \"38%\",\n                    away: \"41%\"\n                },\n                rebounds: {\n                    home: 28,\n                    away: 31\n                }\n            }\n        },\n        {\n            id: 3,\n            sport: \"Tennis\",\n            league: \"ATP Masters\",\n            homeTeam: \"Djokovic\",\n            awayTeam: \"Alcaraz\",\n            score: {\n                home: \"6-4, 3-2\",\n                away: \"\"\n            },\n            time: \"Set 2\",\n            homeOdds: {\n                win: 1.65\n            },\n            awayOdds: {\n                win: 2.25\n            },\n            events: [\n                {\n                    time: \"Game 5\",\n                    type: \"break\",\n                    team: \"home\",\n                    player: \"Djokovic breaks\"\n                },\n                {\n                    time: \"Game 3\",\n                    type: \"ace\",\n                    team: \"away\",\n                    player: \"Alcaraz ace\"\n                }\n            ],\n            stats: {\n                aces: {\n                    home: 8,\n                    away: 12\n                },\n                winners: {\n                    home: 15,\n                    away: 18\n                },\n                unforced: {\n                    home: 7,\n                    away: 11\n                }\n            }\n        }\n    ];\n    const currentMatch = liveMatches[selectedMatch];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                                children: \"Live Betting\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80\",\n                                children: \"Bet on live matches with real-time odds and instant updates\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    glass: true,\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Live Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 text-red-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-red-400 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"LIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: liveMatches.map((match, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedMatch(index),\n                                                    className: \"w-full p-3 rounded-lg transition-all duration-200 text-left \".concat(selectedMatch === index ? \"bg-blue-600 text-white\" : \"bg-white/5 text-white/80 hover:bg-white/10\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-400 mb-1\",\n                                                            children: match.league\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: [\n                                                                match.homeTeam,\n                                                                \" vs \",\n                                                                match.awayTeam\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 opacity-75\",\n                                                            children: match.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, match.id, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.3\n                                },\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    glass: true,\n                                    className: \"p-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-red-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 bg-red-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"LIVE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-medium\",\n                                                            children: currentMatch.league\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                                        className: \"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors\",\n                                                        children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-8 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: currentMatch.homeTeam\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: typeof currentMatch.score.home === \"number\" ? currentMatch.score.home : currentMatch.score.home\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/60 mb-2\",\n                                                                children: \"VS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: currentMatch.time\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: currentMatch.awayTeam\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: typeof currentMatch.score.away === \"number\" ? currentMatch.score.away : currentMatch.score.away || \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-green-500/10 border-green-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-400 font-semibold mb-2\",\n                                                            children: [\n                                                                currentMatch.homeTeam,\n                                                                \" Win\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.homeOdds.win\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"home\",\n                                                                label: \"\".concat(currentMatch.homeTeam, \" Win\"),\n                                                                odds: currentMatch.homeOdds.win\n                                                            },\n                                                            variant: \"home\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentMatch.homeOdds.draw && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-yellow-500/10 border-yellow-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-yellow-400 font-semibold mb-2\",\n                                                            children: \"Draw\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.homeOdds.draw\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"draw\",\n                                                                label: \"Draw\",\n                                                                odds: currentMatch.homeOdds.draw\n                                                            },\n                                                            variant: \"draw\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-red-500/10 border-red-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-red-400 font-semibold mb-2\",\n                                                            children: [\n                                                                currentMatch.awayTeam,\n                                                                \" Win\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.awayOdds.win\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"away\",\n                                                                label: \"\".concat(currentMatch.awayTeam, \" Win\"),\n                                                                odds: currentMatch.awayOdds.win\n                                                            },\n                                                            variant: \"away\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Recent Events\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: currentMatch.events.slice(0, 3).map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: index * 0.1\n                                                            },\n                                                            className: \"flex items-center space-x-3 p-3 bg-white/5 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-400 font-mono text-sm\",\n                                                                    children: event.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-white\",\n                                                                    children: [\n                                                                        event.type === \"goal\" && \"⚽\",\n                                                                        event.type === \"score\" && \"\\uD83C\\uDFC0\",\n                                                                        event.type === \"break\" && \"\\uD83C\\uDFBE\",\n                                                                        event.type === \"ace\" && \"\\uD83C\\uDFBE\",\n                                                                        event.player\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full \".concat(event.team === \"home\" ? \"bg-green-400\" : \"bg-red-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"More Markets\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BettingMarkets__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    match: {\n                                                        id: currentMatch.id.toString(),\n                                                        homeTeam: currentMatch.homeTeam,\n                                                        awayTeam: currentMatch.awayTeam,\n                                                        league: currentMatch.league,\n                                                        startTime: new Date(),\n                                                        isLive: true\n                                                    },\n                                                    markets: {\n                                                        matchResult: {\n                                                            home: currentMatch.homeOdds.win,\n                                                            draw: currentMatch.homeOdds.draw,\n                                                            away: currentMatch.awayOdds.win\n                                                        },\n                                                        overUnder: [\n                                                            {\n                                                                line: 2.5,\n                                                                over: 1.85,\n                                                                under: 1.95\n                                                            },\n                                                            {\n                                                                line: 3.5,\n                                                                over: 2.80,\n                                                                under: 1.40\n                                                            },\n                                                            {\n                                                                line: 1.5,\n                                                                over: 1.25,\n                                                                under: 3.75\n                                                            }\n                                                        ],\n                                                        handicap: [\n                                                            {\n                                                                line: -1,\n                                                                home: 3.20,\n                                                                away: 1.35\n                                                            },\n                                                            {\n                                                                line: 0,\n                                                                home: 2.10,\n                                                                away: 1.75\n                                                            },\n                                                            {\n                                                                line: +1,\n                                                                home: 1.45,\n                                                                away: 2.75\n                                                            }\n                                                        ],\n                                                        bothTeamsScore: {\n                                                            yes: 1.65,\n                                                            no: 2.25\n                                                        }\n                                                    },\n                                                    expanded: false\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Match Statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: Object.entries(currentMatch.stats).map((param)=>{\n                                                        let [key, value] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/5 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/60 text-sm mb-2 capitalize\",\n                                                                    children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-semibold\",\n                                                                            children: typeof value.home === \"number\" ? value.home : value.home\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-semibold\",\n                                                                            children: typeof value.away === \"number\" ? value.away : value.away\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(LiveBettingPage, \"dMIiH3JGMWJUtBS88imuaxNGw80=\");\n_c = LiveBettingPage;\nvar _c;\n$RefreshReg$(_c, \"LiveBettingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/live/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Betting/BettingMarkets.tsx":
/*!***************************************************!*\
  !*** ./src/components/Betting/BettingMarkets.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _BetButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BetButton */ \"(app-pages-browser)/./src/components/Betting/BetButton.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Card */ \"(app-pages-browser)/./src/components/UI/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst BettingMarkets = (param)=>{\n    let { match, markets = {}, expanded = false } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(expanded);\n    // Default markets if not provided\n    const defaultMarkets = {\n        matchResult: {\n            home: 2.45,\n            draw: 3.20,\n            away: 2.80\n        },\n        overUnder: [\n            {\n                line: 2.5,\n                over: 1.85,\n                under: 1.95\n            },\n            {\n                line: 3.5,\n                over: 2.80,\n                under: 1.40\n            }\n        ],\n        handicap: [\n            {\n                line: -1,\n                home: 3.20,\n                away: 1.35\n            },\n            {\n                line: 0,\n                home: 2.10,\n                away: 1.75\n            },\n            {\n                line: +1,\n                home: 1.45,\n                away: 2.75\n            }\n        ],\n        bothTeamsScore: {\n            yes: 1.65,\n            no: 2.25\n        },\n        ...markets\n    };\n    const marketSections = [\n        {\n            id: \"match-result\",\n            title: \"Match Result\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"home\",\n                            label: match.homeTeam,\n                            odds: defaultMarkets.matchResult.home\n                        },\n                        variant: \"home\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined),\n                    defaultMarkets.matchResult.draw && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"draw\",\n                            label: \"Draw\",\n                            odds: defaultMarkets.matchResult.draw\n                        },\n                        variant: \"draw\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"away\",\n                            label: match.awayTeam,\n                            odds: defaultMarkets.matchResult.away\n                        },\n                        variant: \"away\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: \"over-under\",\n            title: \"Total Goals\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: defaultMarkets.overUnder.map((market, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm text-center\",\n                                children: [\n                                    \"O/U \",\n                                    market.line\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"over\",\n                                    label: \"Over \".concat(market.line),\n                                    odds: market.over,\n                                    line: market.line\n                                },\n                                variant: \"over\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"under\",\n                                    label: \"Under \".concat(market.line),\n                                    odds: market.under,\n                                    line: market.line\n                                },\n                                variant: \"under\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: \"handicap\",\n            title: \"Asian Handicap\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: defaultMarkets.handicap.map((market, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm text-center\",\n                                children: market.line > 0 ? \"+\".concat(market.line) : market.line\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"handicap\",\n                                    label: \"\".concat(match.homeTeam, \" \").concat(market.line > 0 ? \"+\".concat(market.line) : market.line),\n                                    odds: market.home,\n                                    line: market.line\n                                },\n                                variant: \"home\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"handicap\",\n                                    label: \"\".concat(match.awayTeam, \" \").concat(market.line < 0 ? \"+\".concat(Math.abs(market.line)) : \"-\".concat(market.line)),\n                                    odds: market.away,\n                                    line: -market.line\n                                },\n                                variant: \"away\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: \"both-teams-score\",\n            title: \"Both Teams to Score\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"yes\",\n                            label: \"Yes\",\n                            odds: defaultMarkets.bothTeamsScore.yes\n                        },\n                        variant: \"over\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"no\",\n                            label: \"No\",\n                            odds: defaultMarkets.bothTeamsScore.no\n                        },\n                        variant: \"under\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        glass: true,\n        className: \"p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-medium mb-3\",\n                        children: \"Match Result\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    marketSections[0].content\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsExpanded(!isExpanded),\n                className: \"w-full flex items-center justify-center space-x-2 py-2 text-white/60 hover:text-white transition-colors border-t border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: isExpanded ? \"Less Markets\" : \"More Markets\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: false,\n                animate: {\n                    height: isExpanded ? \"auto\" : 0,\n                    opacity: isExpanded ? 1 : 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-4 space-y-6\",\n                    children: marketSections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-medium mb-3\",\n                                    children: section.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, undefined),\n                                section.content\n                            ]\n                        }, section.id, true, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BettingMarkets, \"RMFlZSDZtFF+H26TEQMXP7pVvZw=\");\n_c = BettingMarkets;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BettingMarkets);\nvar _c;\n$RefreshReg$(_c, \"BettingMarkets\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Betting/BettingMarkets.tsx\n"));

/***/ })

});