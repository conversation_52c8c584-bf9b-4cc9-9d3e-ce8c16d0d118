/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/live/page";
exports.ids = ["app/live/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flive%2Fpage&page=%2Flive%2Fpage&appPaths=%2Flive%2Fpage&pagePath=private-next-app-dir%2Flive%2Fpage.tsx&appDir=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTahsinBet%20Yeni%20Website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flive%2Fpage&page=%2Flive%2Fpage&appPaths=%2Flive%2Fpage&pagePath=private-next-app-dir%2Flive%2Fpage.tsx&appDir=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTahsinBet%20Yeni%20Website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'live',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/live/page.tsx */ \"(rsc)/./src/app/live/page.tsx\")), \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/live/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/live/page\",\n        pathname: \"/live\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flive%2Fpage&page=%2Flive%2Fpage&appPaths=%2Flive%2Fpage&pagePath=private-next-app-dir%2Flive%2Fpage.tsx&appDir=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTahsinBet%20Yeni%20Website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp%5Clive%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp%5Clive%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/live/page.tsx */ \"(ssr)/./src/app/live/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1RhaHNpbkJldCUyMFllbmklMjBXZWJzaXRlJTVDc3JjJTVDYXBwJTVDbGl2ZSU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RhaHNpbmJldC13ZWJzaXRlLz8zYTA5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVGFoc2luQmV0IFllbmkgV2Vic2l0ZVxcXFxzcmNcXFxcYXBwXFxcXGxpdmVcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp%5Clive%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/live/page.tsx":
/*!*******************************!*\
  !*** ./src/app/live/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LiveBettingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/Layout */ \"(ssr)/./src/components/Layout/Layout.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Card */ \"(ssr)/./src/components/UI/Card.tsx\");\n/* harmony import */ var _components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Betting/BetButton */ \"(ssr)/./src/components/Betting/BetButton.tsx\");\n/* harmony import */ var _components_Betting_BettingMarkets__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Betting/BettingMarkets */ \"(ssr)/./src/components/Betting/BettingMarkets.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LiveBettingPage() {\n    const [selectedMatch, setSelectedMatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    const liveMatches = [\n        {\n            id: 1,\n            sport: \"Football\",\n            league: \"Premier League\",\n            homeTeam: \"Manchester City\",\n            awayTeam: \"Arsenal\",\n            score: {\n                home: 2,\n                away: 1\n            },\n            time: \"67'\",\n            homeOdds: {\n                win: 1.45,\n                draw: 4.20,\n                lose: 6.50\n            },\n            awayOdds: {\n                win: 6.50,\n                draw: 4.20,\n                lose: 1.45\n            },\n            events: [\n                {\n                    time: \"65'\",\n                    type: \"goal\",\n                    team: \"home\",\n                    player: \"Haaland\"\n                },\n                {\n                    time: \"43'\",\n                    type: \"goal\",\n                    team: \"away\",\n                    player: \"Saka\"\n                },\n                {\n                    time: \"23'\",\n                    type: \"goal\",\n                    team: \"home\",\n                    player: \"De Bruyne\"\n                }\n            ],\n            stats: {\n                possession: {\n                    home: 58,\n                    away: 42\n                },\n                shots: {\n                    home: 12,\n                    away: 8\n                },\n                corners: {\n                    home: 6,\n                    away: 3\n                }\n            }\n        },\n        {\n            id: 2,\n            sport: \"Basketball\",\n            league: \"NBA\",\n            homeTeam: \"Lakers\",\n            awayTeam: \"Warriors\",\n            score: {\n                home: 89,\n                away: 94\n            },\n            time: \"Q3 8:45\",\n            homeOdds: {\n                win: 2.10,\n                spread: 1.90\n            },\n            awayOdds: {\n                win: 1.75,\n                spread: 1.90\n            },\n            events: [\n                {\n                    time: \"9:12\",\n                    type: \"score\",\n                    team: \"away\",\n                    player: \"Curry 3PT\"\n                },\n                {\n                    time: \"9:45\",\n                    type: \"score\",\n                    team: \"home\",\n                    player: \"LeBron 2PT\"\n                }\n            ],\n            stats: {\n                fieldGoal: {\n                    home: \"45%\",\n                    away: \"52%\"\n                },\n                threePoint: {\n                    home: \"38%\",\n                    away: \"41%\"\n                },\n                rebounds: {\n                    home: 28,\n                    away: 31\n                }\n            }\n        },\n        {\n            id: 3,\n            sport: \"Tennis\",\n            league: \"ATP Masters\",\n            homeTeam: \"Djokovic\",\n            awayTeam: \"Alcaraz\",\n            score: {\n                home: \"6-4, 3-2\",\n                away: \"\"\n            },\n            time: \"Set 2\",\n            homeOdds: {\n                win: 1.65\n            },\n            awayOdds: {\n                win: 2.25\n            },\n            events: [\n                {\n                    time: \"Game 5\",\n                    type: \"break\",\n                    team: \"home\",\n                    player: \"Djokovic breaks\"\n                },\n                {\n                    time: \"Game 3\",\n                    type: \"ace\",\n                    team: \"away\",\n                    player: \"Alcaraz ace\"\n                }\n            ],\n            stats: {\n                aces: {\n                    home: 8,\n                    away: 12\n                },\n                winners: {\n                    home: 15,\n                    away: 18\n                },\n                unforced: {\n                    home: 7,\n                    away: 11\n                }\n            }\n        }\n    ];\n    const currentMatch = liveMatches[selectedMatch];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                                children: \"Live Betting\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80\",\n                                children: \"Bet on live matches with real-time odds and instant updates\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    glass: true,\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Live Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 text-red-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-red-400 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"LIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: liveMatches.map((match, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedMatch(index),\n                                                    className: `w-full p-3 rounded-lg transition-all duration-200 text-left ${selectedMatch === index ? \"bg-blue-600 text-white\" : \"bg-white/5 text-white/80 hover:bg-white/10\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-400 mb-1\",\n                                                            children: match.league\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: [\n                                                                match.homeTeam,\n                                                                \" vs \",\n                                                                match.awayTeam\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 opacity-75\",\n                                                            children: match.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, match.id, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.3\n                                },\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    glass: true,\n                                    className: \"p-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-red-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 bg-red-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"LIVE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-medium\",\n                                                            children: currentMatch.league\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                                        className: \"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors\",\n                                                        children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-8 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: currentMatch.homeTeam\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: typeof currentMatch.score.home === \"number\" ? currentMatch.score.home : currentMatch.score.home\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/60 mb-2\",\n                                                                children: \"VS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: currentMatch.time\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: currentMatch.awayTeam\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: typeof currentMatch.score.away === \"number\" ? currentMatch.score.away : currentMatch.score.away || \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-green-500/10 border-green-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-400 font-semibold mb-2\",\n                                                            children: [\n                                                                currentMatch.homeTeam,\n                                                                \" Win\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.homeOdds.win\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"home\",\n                                                                label: `${currentMatch.homeTeam} Win`,\n                                                                odds: currentMatch.homeOdds.win\n                                                            },\n                                                            variant: \"home\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentMatch.homeOdds.draw && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-yellow-500/10 border-yellow-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-yellow-400 font-semibold mb-2\",\n                                                            children: \"Draw\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.homeOdds.draw\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"draw\",\n                                                                label: \"Draw\",\n                                                                odds: currentMatch.homeOdds.draw\n                                                            },\n                                                            variant: \"draw\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-red-500/10 border-red-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-red-400 font-semibold mb-2\",\n                                                            children: [\n                                                                currentMatch.awayTeam,\n                                                                \" Win\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.awayOdds.win\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"away\",\n                                                                label: `${currentMatch.awayTeam} Win`,\n                                                                odds: currentMatch.awayOdds.win\n                                                            },\n                                                            variant: \"away\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Recent Events\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: currentMatch.events.slice(0, 3).map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: index * 0.1\n                                                            },\n                                                            className: \"flex items-center space-x-3 p-3 bg-white/5 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-400 font-mono text-sm\",\n                                                                    children: event.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-white\",\n                                                                    children: [\n                                                                        event.type === \"goal\" && \"⚽\",\n                                                                        event.type === \"score\" && \"\\uD83C\\uDFC0\",\n                                                                        event.type === \"break\" && \"\\uD83C\\uDFBE\",\n                                                                        event.type === \"ace\" && \"\\uD83C\\uDFBE\",\n                                                                        event.player\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `w-3 h-3 rounded-full ${event.team === \"home\" ? \"bg-green-400\" : \"bg-red-400\"}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"More Markets\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BettingMarkets__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    match: {\n                                                        id: currentMatch.id.toString(),\n                                                        homeTeam: currentMatch.homeTeam,\n                                                        awayTeam: currentMatch.awayTeam,\n                                                        league: currentMatch.league,\n                                                        startTime: new Date(),\n                                                        isLive: true\n                                                    },\n                                                    markets: {\n                                                        matchResult: {\n                                                            home: currentMatch.homeOdds.win,\n                                                            draw: currentMatch.homeOdds.draw,\n                                                            away: currentMatch.awayOdds.win\n                                                        },\n                                                        overUnder: [\n                                                            {\n                                                                line: 2.5,\n                                                                over: 1.85,\n                                                                under: 1.95\n                                                            },\n                                                            {\n                                                                line: 3.5,\n                                                                over: 2.80,\n                                                                under: 1.40\n                                                            },\n                                                            {\n                                                                line: 1.5,\n                                                                over: 1.25,\n                                                                under: 3.75\n                                                            }\n                                                        ],\n                                                        handicap: [\n                                                            {\n                                                                line: -1,\n                                                                home: 3.20,\n                                                                away: 1.35\n                                                            },\n                                                            {\n                                                                line: 0,\n                                                                home: 2.10,\n                                                                away: 1.75\n                                                            },\n                                                            {\n                                                                line: +1,\n                                                                home: 1.45,\n                                                                away: 2.75\n                                                            }\n                                                        ],\n                                                        bothTeamsScore: {\n                                                            yes: 1.65,\n                                                            no: 2.25\n                                                        }\n                                                    },\n                                                    expanded: false\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Match Statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: Object.entries(currentMatch.stats).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/5 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/60 text-sm mb-2 capitalize\",\n                                                                    children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-semibold\",\n                                                                            children: typeof value.home === \"number\" ? value.home : value.home\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-semibold\",\n                                                                            children: typeof value.away === \"number\" ? value.away : value.away\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/live/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Betting/BetButton.tsx":
/*!**********************************************!*\
  !*** ./src/components/Betting/BetButton.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(ssr)/./src/contexts/BettingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst BetButton = ({ matchId, match, selection, className = \"\", size = \"md\", variant = \"default\" })=>{\n    const { state, addToBetSlip } = (0,_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const [previousOdds, setPreviousOdds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selection.odds);\n    const [oddsDirection, setOddsDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if this selection is already in the bet slip\n    const isInSlip = state.betSlip.items.some((item)=>item.matchId === matchId && item.selection.type === selection.type);\n    // Detect odds changes\n    if (selection.odds !== previousOdds) {\n        setOddsDirection(selection.odds > previousOdds ? \"up\" : \"down\");\n        setPreviousOdds(selection.odds);\n        // Clear direction after animation\n        setTimeout(()=>setOddsDirection(null), 1000);\n    }\n    const handleClick = ()=>{\n        const betSlipItem = {\n            id: `${matchId}-${selection.type}`,\n            matchId,\n            match,\n            selection,\n            amount: 0,\n            potentialWin: 0\n        };\n        addToBetSlip(betSlipItem);\n    };\n    const getVariantClasses = ()=>{\n        const baseClasses = \"relative overflow-hidden transition-all duration-200 font-semibold rounded-lg border-2\";\n        switch(variant){\n            case \"home\":\n                return `${baseClasses} ${isInSlip ? \"bg-green-600 border-green-500 text-white\" : \"bg-green-500/10 border-green-500/50 text-green-400 hover:bg-green-500/20\"}`;\n            case \"away\":\n                return `${baseClasses} ${isInSlip ? \"bg-red-600 border-red-500 text-white\" : \"bg-red-500/10 border-red-500/50 text-red-400 hover:bg-red-500/20\"}`;\n            case \"draw\":\n                return `${baseClasses} ${isInSlip ? \"bg-yellow-600 border-yellow-500 text-white\" : \"bg-yellow-500/10 border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/20\"}`;\n            case \"over\":\n                return `${baseClasses} ${isInSlip ? \"bg-blue-600 border-blue-500 text-white\" : \"bg-blue-500/10 border-blue-500/50 text-blue-400 hover:bg-blue-500/20\"}`;\n            case \"under\":\n                return `${baseClasses} ${isInSlip ? \"bg-purple-600 border-purple-500 text-white\" : \"bg-purple-500/10 border-purple-500/50 text-purple-400 hover:bg-purple-500/20\"}`;\n            default:\n                return `${baseClasses} ${isInSlip ? \"bg-blue-600 border-blue-500 text-white\" : \"bg-white/10 border-white/20 text-white hover:bg-white/20\"}`;\n        }\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"px-2 py-1 text-xs min-w-[60px]\";\n            case \"lg\":\n                return \"px-6 py-3 text-base min-w-[100px]\";\n            default:\n                return \"px-4 py-2 text-sm min-w-[80px]\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n        onClick: handleClick,\n        className: `${getVariantClasses()} ${getSizeClasses()} ${className}`,\n        whileHover: {\n            scale: 1.02\n        },\n        whileTap: {\n            scale: 0.98\n        },\n        layout: true,\n        children: [\n            oddsDirection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: `absolute top-0 right-0 w-3 h-3 ${oddsDirection === \"up\" ? \"text-green-400\" : \"text-red-400\"}`,\n                children: oddsDirection === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, undefined),\n            match.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1 left-1 w-2 h-2 bg-red-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    scale: 1.1\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.2\n                },\n                className: \"flex flex-col items-center\",\n                children: [\n                    size !== \"sm\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs opacity-80 mb-1 truncate max-w-full\",\n                        children: selection.label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-bold\",\n                        children: selection.odds.toFixed(2)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, selection.odds, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            isInSlip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                className: \"absolute inset-0 bg-white/20 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 bg-white rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-current rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 bg-white/20 rounded-lg\",\n                initial: {\n                    scale: 0,\n                    opacity: 0.5\n                },\n                animate: {\n                    scale: 1.5,\n                    opacity: 0\n                },\n                transition: {\n                    duration: 0.6\n                }\n            }, `ripple-${selection.odds}`, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BetButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Betting/BetButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Betting/BetSlip.tsx":
/*!********************************************!*\
  !*** ./src/components/Betting/BetSlip.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calculator,Check,Trash2,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(ssr)/./src/contexts/BettingContext.tsx\");\n/* harmony import */ var _components_UI_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Button */ \"(ssr)/./src/components/UI/Button.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/UI/Card */ \"(ssr)/./src/components/UI/Card.tsx\");\n/* harmony import */ var _utils_bettingValidation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/bettingValidation */ \"(ssr)/./src/utils/bettingValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst BetSlip = ()=>{\n    const { state, removeFromBetSlip, updateBetAmount, clearBetSlip, setBetType, placeBets, toggleBetSlip } = (0,_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const [showConfirmation, setShowConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [acceptedTerms, setAcceptedTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const quickAmounts = [\n        10,\n        25,\n        50,\n        100\n    ];\n    const handleQuickAmount = (itemId, amount)=>{\n        updateBetAmount(itemId, amount);\n    };\n    const handleAmountChange = (itemId, value)=>{\n        const amount = parseFloat(value) || 0;\n        if (amount >= 0 && amount <= state.bettingLimits.maxBet) {\n            updateBetAmount(itemId, amount);\n        }\n    };\n    const getValidationErrors = ()=>{\n        return (0,_utils_bettingValidation__WEBPACK_IMPORTED_MODULE_5__.validateBetSlip)(state.betSlip.items, state.userBalance, state.bettingLimits, state.betSlip.betType);\n    };\n    const canPlaceBets = ()=>{\n        const errors = getValidationErrors();\n        return errors.length === 0 && acceptedTerms;\n    };\n    const handlePlaceBets = async ()=>{\n        if (!acceptedTerms) {\n            setShowConfirmation(true);\n            return;\n        }\n        await placeBets();\n    };\n    const confirmPlaceBets = async ()=>{\n        setShowConfirmation(false);\n        await placeBets();\n    };\n    if (!state.isSlipOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\",\n                onClick: toggleBetSlip\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    x: \"100%\"\n                },\n                animate: {\n                    x: 0\n                },\n                exit: {\n                    x: \"100%\"\n                },\n                transition: {\n                    type: \"spring\",\n                    damping: 25,\n                    stiffness: 200\n                },\n                className: \"fixed right-0 top-0 h-full w-full max-w-md bg-slate-900 border-l border-white/20 z-50 overflow-hidden flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-white/20 bg-slate-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Bet Slip\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            state.betSlip.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearBetSlip,\n                                                className: \"p-2 text-white/60 hover:text-red-400 transition-colors\",\n                                                title: \"Clear all bets\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleBetSlip,\n                                                className: \"p-2 text-white/60 hover:text-white transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            state.betSlip.items.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 flex bg-white/10 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setBetType(\"single\"),\n                                        className: `flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${state.betSlip.betType === \"single\" ? \"bg-blue-600 text-white\" : \"text-white/80 hover:text-white\"}`,\n                                        children: \"Single Bets\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setBetType(\"parlay\"),\n                                        className: `flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all ${state.betSlip.betType === \"parlay\" ? \"bg-blue-600 text-white\" : \"text-white/80 hover:text-white\"}`,\n                                        children: \"Parlay\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: state.betSlip.items.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-12 h-12 text-white/40 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-2\",\n                                    children: \"Your bet slip is empty\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/40 text-sm\",\n                                    children: \"Click on odds to add selections to your bet slip\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: state.betSlip.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        glass: true,\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400 text-xs font-medium mb-1\",\n                                                                children: item.match.league\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium text-sm mb-1\",\n                                                                children: [\n                                                                    item.match.homeTeam,\n                                                                    \" vs \",\n                                                                    item.match.awayTeam\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/80 text-sm\",\n                                                                children: item.selection.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeFromBetSlip(item.id),\n                                                        className: \"p-1 text-white/60 hover:text-red-400 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: \"Odds\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: item.selection.odds.toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-1 mb-3\",\n                                                children: quickAmounts.map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuickAmount(item.id, amount),\n                                                        className: \"py-1 px-2 bg-white/10 hover:bg-white/20 text-white text-xs rounded transition-colors\",\n                                                        children: [\n                                                            \"€\",\n                                                            amount\n                                                        ]\n                                                    }, amount, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white/60 text-xs mb-1\",\n                                                        children: [\n                                                            \"Stake (€\",\n                                                            state.bettingLimits.minBet,\n                                                            \" - €\",\n                                                            state.bettingLimits.maxBet,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        min: state.bettingLimits.minBet,\n                                                        max: state.bettingLimits.maxBet,\n                                                        step: \"0.01\",\n                                                        value: item.amount || \"\",\n                                                        onChange: (e)=>handleAmountChange(item.id, e.target.value),\n                                                        className: \"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-blue-500\",\n                                                        placeholder: \"Enter amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Potential Win\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 font-semibold\",\n                                                        children: [\n                                                            \"€\",\n                                                            item.potentialWin.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.id, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    state.betSlip.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-white/20 bg-slate-800/50\",\n                        children: [\n                            state.betSlip.betType === \"parlay\" && state.betSlip.parlayOdds && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-purple-500/20 border border-purple-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-300\",\n                                            children: \"Parlay Odds\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-300 font-semibold\",\n                                            children: state.betSlip.parlayOdds.toFixed(2)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"Total Stake\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    \"€\",\n                                                    state.betSlip.totalStake.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"Potential Win\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-semibold\",\n                                                children: [\n                                                    \"€\",\n                                                    state.betSlip.totalPotentialWin.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60\",\n                                                children: \"Your Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: [\n                                                    \"€\",\n                                                    state.userBalance.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined),\n                            (()=>{\n                                const validationErrors = getValidationErrors();\n                                const errorMessages = (0,_utils_bettingValidation__WEBPACK_IMPORTED_MODULE_5__.formatValidationErrors)(validationErrors);\n                                return errorMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3 p-2 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-300 text-sm\",\n                                                children: message\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, undefined));\n                            })(),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fullWidth: true,\n                                size: \"lg\",\n                                disabled: !canPlaceBets(),\n                                isLoading: state.isLoading,\n                                onClick: handlePlaceBets,\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 25\n                                }, void 0),\n                                children: [\n                                    \"Place Bet\",\n                                    state.betSlip.items.length > 1 ? \"s\" : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-start space-x-2 mt-3 text-xs text-white/60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: acceptedTerms,\n                                        onChange: (e)=>setAcceptedTerms(e.target.checked),\n                                        className: \"w-3 h-3 mt-0.5 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"I accept the\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/terms\",\n                                                className: \"text-blue-400 hover:text-blue-300\",\n                                                children: \"terms and conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: showConfirmation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-slate-800 rounded-xl p-6 max-w-md w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calculator_Check_Trash2_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: [\n                                            \"Confirm Your Bet\",\n                                            state.betSlip.items.length > 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60\",\n                                        children: [\n                                            \"You are about to place \",\n                                            state.betSlip.items.length,\n                                            \" bet\",\n                                            state.betSlip.items.length > 1 ? \"s\" : \"\",\n                                            \"for a total stake of €\",\n                                            state.betSlip.totalStake.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: acceptedTerms,\n                                            onChange: (e)=>setAcceptedTerms(e.target.checked),\n                                            className: \"w-4 h-4 mt-0.5 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/80 text-sm\",\n                                            children: \"I confirm that I am 18+ and accept the terms and conditions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"outline\",\n                                        fullWidth: true,\n                                        onClick: ()=>setShowConfirmation(false),\n                                        className: \"border-white/30 text-white hover:bg-white/10\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        fullWidth: true,\n                                        disabled: !acceptedTerms,\n                                        onClick: confirmPlaceBets,\n                                        isLoading: state.isLoading,\n                                        children: \"Confirm Bet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlip.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BetSlip);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Betting/BetSlip.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Betting/BetSlipToggle.tsx":
/*!**************************************************!*\
  !*** ./src/components/Betting/BetSlipToggle.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(ssr)/./src/contexts/BettingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BetSlipToggle = ()=>{\n    const { state, toggleBetSlip } = (0,_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_1__.useBetting)();\n    if (state.betSlip.items.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n            initial: {\n                scale: 0,\n                opacity: 0\n            },\n            animate: {\n                scale: 1,\n                opacity: 1\n            },\n            exit: {\n                scale: 0,\n                opacity: 0\n            },\n            whileHover: {\n                scale: 1.05\n            },\n            whileTap: {\n                scale: 0.95\n            },\n            onClick: toggleBetSlip,\n            className: \"fixed bottom-6 right-6 z-30 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 lg:hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                scale: 0\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\",\n                            children: state.betSlip.items.length\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute inset-0 bg-white/20 rounded-full\",\n                            initial: {\n                                scale: 1,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1.5,\n                                opacity: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                repeat: Infinity\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined),\n                state.betSlip.totalStake > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap\",\n                    children: [\n                        \"€\",\n                        state.betSlip.totalStake.toFixed(2)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetSlipToggle.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BetSlipToggle);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Betting/BetSlipToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Betting/BettingMarkets.tsx":
/*!***************************************************!*\
  !*** ./src/components/Betting/BettingMarkets.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _BetButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BetButton */ \"(ssr)/./src/components/Betting/BetButton.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Card */ \"(ssr)/./src/components/UI/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst BettingMarkets = ({ match, markets = {}, expanded = false })=>{\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(expanded);\n    // Default markets if not provided\n    const defaultMarkets = {\n        matchResult: {\n            home: 2.45,\n            draw: 3.20,\n            away: 2.80\n        },\n        overUnder: [\n            {\n                line: 2.5,\n                over: 1.85,\n                under: 1.95\n            },\n            {\n                line: 3.5,\n                over: 2.80,\n                under: 1.40\n            }\n        ],\n        handicap: [\n            {\n                line: -1,\n                home: 3.20,\n                away: 1.35\n            },\n            {\n                line: 0,\n                home: 2.10,\n                away: 1.75\n            },\n            {\n                line: +1,\n                home: 1.45,\n                away: 2.75\n            }\n        ],\n        bothTeamsScore: {\n            yes: 1.65,\n            no: 2.25\n        },\n        ...markets\n    };\n    const marketSections = [\n        {\n            id: \"match-result\",\n            title: \"Match Result\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"home\",\n                            label: match.homeTeam,\n                            odds: defaultMarkets.matchResult.home\n                        },\n                        variant: \"home\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined),\n                    defaultMarkets.matchResult.draw && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"draw\",\n                            label: \"Draw\",\n                            odds: defaultMarkets.matchResult.draw\n                        },\n                        variant: \"draw\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"away\",\n                            label: match.awayTeam,\n                            odds: defaultMarkets.matchResult.away\n                        },\n                        variant: \"away\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: \"over-under\",\n            title: \"Total Goals\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: defaultMarkets.overUnder.map((market, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm text-center\",\n                                children: [\n                                    \"O/U \",\n                                    market.line\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"over\",\n                                    label: `Over ${market.line}`,\n                                    odds: market.over,\n                                    line: market.line\n                                },\n                                variant: \"over\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"under\",\n                                    label: `Under ${market.line}`,\n                                    odds: market.under,\n                                    line: market.line\n                                },\n                                variant: \"under\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: \"handicap\",\n            title: \"Asian Handicap\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: defaultMarkets.handicap.map((market, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm text-center\",\n                                children: market.line > 0 ? `+${market.line}` : market.line\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"handicap\",\n                                    label: `${match.homeTeam} ${market.line > 0 ? `+${market.line}` : market.line}`,\n                                    odds: market.home,\n                                    line: market.line\n                                },\n                                variant: \"home\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchId: match.id,\n                                match: match,\n                                selection: {\n                                    type: \"handicap\",\n                                    label: `${match.awayTeam} ${market.line < 0 ? `+${Math.abs(market.line)}` : `-${market.line}`}`,\n                                    odds: market.away,\n                                    line: -market.line\n                                },\n                                variant: \"away\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            id: \"both-teams-score\",\n            title: \"Both Teams to Score\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"yes\",\n                            label: \"Yes\",\n                            odds: defaultMarkets.bothTeamsScore.yes\n                        },\n                        variant: \"over\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BetButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        matchId: match.id,\n                        match: match,\n                        selection: {\n                            type: \"no\",\n                            label: \"No\",\n                            odds: defaultMarkets.bothTeamsScore.no\n                        },\n                        variant: \"under\",\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        glass: true,\n        className: \"p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-medium mb-3\",\n                        children: \"Match Result\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    marketSections[0].content\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsExpanded(!isExpanded),\n                className: \"w-full flex items-center justify-center space-x-2 py-2 text-white/60 hover:text-white transition-colors border-t border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: isExpanded ? \"Less Markets\" : \"More Markets\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: false,\n                animate: {\n                    height: isExpanded ? \"auto\" : 0,\n                    opacity: isExpanded ? 1 : 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-4 space-y-6\",\n                    children: marketSections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-medium mb-3\",\n                                    children: section.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, undefined),\n                                section.content\n                            ]\n                        }, section.id, true, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BettingMarkets.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BettingMarkets);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Betting/BettingMarkets.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Trophy,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerSections = [\n        {\n            title: \"Sports\",\n            links: [\n                {\n                    name: \"Football\",\n                    href: \"/sports/football\"\n                },\n                {\n                    name: \"Basketball\",\n                    href: \"/sports/basketball\"\n                },\n                {\n                    name: \"Tennis\",\n                    href: \"/sports/tennis\"\n                },\n                {\n                    name: \"Baseball\",\n                    href: \"/sports/baseball\"\n                },\n                {\n                    name: \"Hockey\",\n                    href: \"/sports/hockey\"\n                }\n            ]\n        },\n        {\n            title: \"Betting\",\n            links: [\n                {\n                    name: \"Live Betting\",\n                    href: \"/live\"\n                },\n                {\n                    name: \"Pre-match\",\n                    href: \"/prematch\"\n                },\n                {\n                    name: \"Casino\",\n                    href: \"/casino\"\n                },\n                {\n                    name: \"Virtual Sports\",\n                    href: \"/virtual\"\n                },\n                {\n                    name: \"Esports\",\n                    href: \"/esports\"\n                }\n            ]\n        },\n        {\n            title: \"Support\",\n            links: [\n                {\n                    name: \"Help Center\",\n                    href: \"/help\"\n                },\n                {\n                    name: \"Contact Us\",\n                    href: \"/contact\"\n                },\n                {\n                    name: \"Live Chat\",\n                    href: \"/chat\"\n                },\n                {\n                    name: \"FAQ\",\n                    href: \"/faq\"\n                },\n                {\n                    name: \"Responsible Gaming\",\n                    href: \"/responsible-gaming\"\n                }\n            ]\n        },\n        {\n            title: \"Company\",\n            links: [\n                {\n                    name: \"About Us\",\n                    href: \"/about\"\n                },\n                {\n                    name: \"Careers\",\n                    href: \"/careers\"\n                },\n                {\n                    name: \"Press\",\n                    href: \"/press\"\n                },\n                {\n                    name: \"Affiliates\",\n                    href: \"/affiliates\"\n                },\n                {\n                    name: \"VIP Program\",\n                    href: \"/vip\"\n                }\n            ]\n        }\n    ];\n    const socialLinks = [\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            href: \"#\",\n            label: \"Facebook\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"#\",\n            label: \"Twitter\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"#\",\n            label: \"Instagram\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"#\",\n            label: \"YouTube\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-black/40 backdrop-blur-md border-t border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold gradient-text\",\n                                            children: \"TahsinBet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-6 max-w-md\",\n                                    children: \"Experience the future of sports betting with competitive odds, live betting, and secure transactions. Join thousands of satisfied customers worldwide.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+****************\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Trophy_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"24/7 Customer Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        footerSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-4\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: section.links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-white/60 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.title, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 pt-8 border-t border-white/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/60\",\n                                        children: \"Follow us:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: social.href,\n                                            className: \"text-white/60 hover:text-white transition-colors duration-200\",\n                                            \"aria-label\": social.label,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, social.label, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Enter your email\",\n                                        className: \"bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-300\",\n                                        children: \"Subscribe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-white/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" TahsinBet. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-white/60 hover:text-white transition-colors duration-200\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-white/60 hover:text-white transition-colors duration-200\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-white/60 hover:text-white transition-colors duration-200\",\n                                        children: \"Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-200 text-sm text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"18+ Only.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            \" Gambling can be addictive. Please play responsibly.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/responsible-gaming\",\n                                className: \"underline hover:no-underline ml-1\",\n                                children: \"Learn more about responsible gaming\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Footer.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,ShoppingCart,Trophy,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,ShoppingCart,Trophy,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,ShoppingCart,Trophy,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,ShoppingCart,Trophy,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,ShoppingCart,Trophy,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(ssr)/./src/contexts/BettingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // This would come from auth context\n    const { state, toggleBetSlip } = (0,_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_3__.useBetting)();\n    const navigationItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Sports\",\n            href: \"/sports\"\n        },\n        {\n            name: \"Live Betting\",\n            href: \"/live\"\n        },\n        {\n            name: \"Casino\",\n            href: \"/casino\"\n        },\n        {\n            name: \"Promotions\",\n            href: \"/promotions\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold gradient-text\",\n                                        children: \"TahsinBet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-white/80 hover:text-white transition-colors duration-200 font-medium\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                state.betSlip.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleBetSlip,\n                                    className: \"relative flex items-center space-x-2 bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white\",\n                                            children: \"Bet Slip\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center\",\n                                            children: state.betSlip.items.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined),\n                                isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-white/80\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Balance:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-green-400\",\n                                                    children: [\n                                                        \"€\",\n                                                        state.userBalance.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"flex items-center space-x-2 bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/login\",\n                                            className: \"text-white/80 hover:text-white transition-colors duration-200 font-medium\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/register\",\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden text-white p-2\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 27\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 55\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    className: \"md:hidden py-4 border-t border-white/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-white/80 hover:text-white transition-colors duration-200 font-medium py-2\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-white/10\",\n                                children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"flex items-center space-x-2 text-white/80 hover:text-white transition-colors duration-200 py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_ShoppingCart_Trophy_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/login\",\n                                            className: \"text-white/80 hover:text-white transition-colors duration-200 font-medium py-2\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/register\",\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold text-center\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Layout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/Layout/Footer.tsx\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(ssr)/./src/contexts/BettingContext.tsx\");\n/* harmony import */ var _components_Betting_BetSlip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Betting/BetSlip */ \"(ssr)/./src/components/Betting/BetSlip.tsx\");\n/* harmony import */ var _components_Betting_BetSlipToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Betting/BetSlipToggle */ \"(ssr)/./src/components/Betting/BetSlipToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Layout = ({ children, showHeader = true, showFooter = true, showBetting = true, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_3__.BettingProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `min-h-screen flex flex-col ${className}`,\n            children: [\n                showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 24\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: `flex-1 ${showHeader ? \"pt-16\" : \"\"}`,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, undefined),\n                showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 24\n                }, undefined),\n                showBetting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetSlip__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetSlipToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UI/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/UI/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Button = ({ children, variant = \"primary\", size = \"md\", isLoading = false, leftIcon, rightIcon, fullWidth = false, className = \"\", disabled, ...props })=>{\n    const baseClasses = \"inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variantClasses = {\n        primary: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl focus:ring-blue-500\",\n        secondary: \"bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500\",\n        outline: \"border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500\",\n        ghost: \"text-gray-600 hover:bg-gray-100 focus:ring-gray-500\",\n        danger: \"bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl focus:ring-red-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-base\",\n        lg: \"px-6 py-3 text-lg\",\n        xl: \"px-8 py-4 text-xl\"\n    };\n    const widthClass = fullWidth ? \"w-full\" : \"\";\n    const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n        whileHover: {\n            scale: disabled || isLoading ? 1 : 1.02\n        },\n        whileTap: {\n            scale: disabled || isLoading ? 1 : 0.98\n        },\n        className: combinedClasses,\n        disabled: disabled || isLoading,\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4 mr-2 animate-spin\"\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\UI\\\\Button.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined),\n            !isLoading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\UI\\\\Button.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined),\n            children,\n            !isLoading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\UI\\\\Button.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\UI\\\\Button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UI/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UI/Card.tsx":
/*!************************************!*\
  !*** ./src/components/UI/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Card = ({ children, className = \"\", hover = true, glass = false, padding = \"md\", onClick })=>{\n    const baseClasses = \"rounded-xl border transition-all duration-300\";\n    const paddingClasses = {\n        none: \"\",\n        sm: \"p-3\",\n        md: \"p-6\",\n        lg: \"p-8\",\n        xl: \"p-10\"\n    };\n    const glassClasses = glass ? \"bg-white/10 backdrop-blur-md border-white/20 shadow-xl\" : \"bg-white border-gray-200 shadow-lg\";\n    const hoverClasses = hover ? \"hover:shadow-xl hover:-translate-y-1 cursor-pointer\" : \"\";\n    const combinedClasses = `${baseClasses} ${glassClasses} ${paddingClasses[padding]} ${hoverClasses} ${className}`;\n    const CardComponent = onClick ? framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div : \"div\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n        className: combinedClasses,\n        onClick: onClick,\n        ...onClick && {\n            whileHover: {\n                scale: 1.02\n            },\n            whileTap: {\n                scale: 0.98\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\UI\\\\Card.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UI/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BettingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/BettingContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BettingProvider: () => (/* binding */ BettingProvider),\n/* harmony export */   useBetting: () => (/* binding */ useBetting)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BettingProvider,useBetting auto */ \n\nconst initialState = {\n    betSlip: {\n        items: [],\n        totalStake: 0,\n        totalPotentialWin: 0,\n        betType: \"single\"\n    },\n    userBalance: 1250.75,\n    bettingLimits: {\n        minBet: 1,\n        maxBet: 1000,\n        maxPayout: 10000,\n        maxParlayLegs: 10\n    },\n    activeBets: [],\n    isSlipOpen: false,\n    isLoading: false,\n    error: null\n};\nfunction bettingReducer(state, action) {\n    switch(action.type){\n        case \"ADD_TO_SLIP\":\n            {\n                const existingIndex = state.betSlip.items.findIndex((item)=>item.id === action.payload.id);\n                let newItems;\n                if (existingIndex >= 0) {\n                    // Replace existing item\n                    newItems = [\n                        ...state.betSlip.items\n                    ];\n                    newItems[existingIndex] = action.payload;\n                } else {\n                    // Add new item\n                    newItems = [\n                        ...state.betSlip.items,\n                        action.payload\n                    ];\n                }\n                const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);\n                return {\n                    ...state,\n                    betSlip: newBetSlip,\n                    isSlipOpen: true\n                };\n            }\n        case \"REMOVE_FROM_SLIP\":\n            {\n                const newItems = state.betSlip.items.filter((item)=>item.id !== action.payload);\n                const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);\n                return {\n                    ...state,\n                    betSlip: newBetSlip\n                };\n            }\n        case \"UPDATE_AMOUNT\":\n            {\n                const newItems = state.betSlip.items.map((item)=>item.id === action.payload.id ? {\n                        ...item,\n                        amount: action.payload.amount,\n                        potentialWin: action.payload.amount * item.selection.odds\n                    } : item);\n                const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);\n                return {\n                    ...state,\n                    betSlip: newBetSlip\n                };\n            }\n        case \"CLEAR_SLIP\":\n            return {\n                ...state,\n                betSlip: {\n                    items: [],\n                    totalStake: 0,\n                    totalPotentialWin: 0,\n                    betType: state.betSlip.betType\n                }\n            };\n        case \"TOGGLE_SLIP\":\n            return {\n                ...state,\n                isSlipOpen: !state.isSlipOpen\n            };\n        case \"SET_BET_TYPE\":\n            {\n                const newBetSlip = calculateBetSlip(state.betSlip.items, action.payload);\n                return {\n                    ...state,\n                    betSlip: newBetSlip\n                };\n            }\n        case \"PLACE_BETS_START\":\n            return {\n                ...state,\n                isLoading: true,\n                error: null\n            };\n        case \"PLACE_BETS_SUCCESS\":\n            return {\n                ...state,\n                isLoading: false,\n                activeBets: [\n                    ...state.activeBets,\n                    ...action.payload\n                ],\n                betSlip: {\n                    items: [],\n                    totalStake: 0,\n                    totalPotentialWin: 0,\n                    betType: state.betSlip.betType\n                },\n                userBalance: state.userBalance - state.betSlip.totalStake\n            };\n        case \"PLACE_BETS_ERROR\":\n            return {\n                ...state,\n                isLoading: false,\n                error: action.payload\n            };\n        case \"UPDATE_BALANCE\":\n            return {\n                ...state,\n                userBalance: action.payload\n            };\n        case \"UPDATE_ODDS\":\n            {\n                const newItems = state.betSlip.items.map((item)=>{\n                    if (item.matchId === action.payload.matchId && item.selection.type === action.payload.selectionType) {\n                        return {\n                            ...item,\n                            selection: {\n                                ...item.selection,\n                                odds: action.payload.newOdds\n                            },\n                            potentialWin: item.amount * action.payload.newOdds\n                        };\n                    }\n                    return item;\n                });\n                const newBetSlip = calculateBetSlip(newItems, state.betSlip.betType);\n                return {\n                    ...state,\n                    betSlip: newBetSlip\n                };\n            }\n        case \"SET_ERROR\":\n            return {\n                ...state,\n                error: action.payload\n            };\n        default:\n            return state;\n    }\n}\nfunction calculateBetSlip(items, betType) {\n    const totalStake = items.reduce((sum, item)=>sum + item.amount, 0);\n    let totalPotentialWin = 0;\n    let parlayOdds = 1;\n    if (betType === \"single\") {\n        totalPotentialWin = items.reduce((sum, item)=>sum + item.potentialWin, 0);\n    } else if (betType === \"parlay\" && items.length > 0) {\n        parlayOdds = items.reduce((odds, item)=>odds * item.selection.odds, 1);\n        totalPotentialWin = totalStake * parlayOdds;\n    }\n    return {\n        items,\n        totalStake,\n        totalPotentialWin,\n        betType,\n        parlayOdds: betType === \"parlay\" ? parlayOdds : undefined\n    };\n}\nconst BettingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction BettingProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(bettingReducer, initialState);\n    const addToBetSlip = (item)=>{\n        dispatch({\n            type: \"ADD_TO_SLIP\",\n            payload: item\n        });\n    };\n    const removeFromBetSlip = (id)=>{\n        dispatch({\n            type: \"REMOVE_FROM_SLIP\",\n            payload: id\n        });\n    };\n    const updateBetAmount = (id, amount)=>{\n        dispatch({\n            type: \"UPDATE_AMOUNT\",\n            payload: {\n                id,\n                amount\n            }\n        });\n    };\n    const clearBetSlip = ()=>{\n        dispatch({\n            type: \"CLEAR_SLIP\"\n        });\n    };\n    const toggleBetSlip = ()=>{\n        dispatch({\n            type: \"TOGGLE_SLIP\"\n        });\n    };\n    const setBetType = (type)=>{\n        dispatch({\n            type: \"SET_BET_TYPE\",\n            payload: type\n        });\n    };\n    const placeBets = async ()=>{\n        dispatch({\n            type: \"PLACE_BETS_START\"\n        });\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Create bet objects\n            const newBets = state.betSlip.items.map((item)=>({\n                    id: Math.random().toString(36).substr(2, 9),\n                    userId: \"user-1\",\n                    matchId: item.matchId,\n                    type: item.selection.type,\n                    amount: item.amount,\n                    odds: item.selection.odds,\n                    potentialWin: item.potentialWin,\n                    status: \"pending\",\n                    placedAt: new Date(),\n                    isLive: item.match.isLive\n                }));\n            dispatch({\n                type: \"PLACE_BETS_SUCCESS\",\n                payload: newBets\n            });\n        } catch (error) {\n            dispatch({\n                type: \"PLACE_BETS_ERROR\",\n                payload: \"Failed to place bets\"\n            });\n        }\n    };\n    const updateOdds = (update)=>{\n        dispatch({\n            type: \"UPDATE_ODDS\",\n            payload: update\n        });\n    };\n    // Simulate real-time odds updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            if (state.betSlip.items.length > 0) {\n                const randomItem = state.betSlip.items[Math.floor(Math.random() * state.betSlip.items.length)];\n                const oddsChange = (Math.random() - 0.5) * 0.2; // ±0.1 change\n                const newOdds = Math.max(1.01, randomItem.selection.odds + oddsChange);\n                updateOdds({\n                    matchId: randomItem.matchId,\n                    marketType: \"match_result\",\n                    selectionType: randomItem.selection.type,\n                    newOdds: Math.round(newOdds * 100) / 100,\n                    timestamp: new Date()\n                });\n            }\n        }, 5000); // Update every 5 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        state.betSlip.items\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BettingContext.Provider, {\n        value: {\n            state,\n            addToBetSlip,\n            removeFromBetSlip,\n            updateBetAmount,\n            clearBetSlip,\n            toggleBetSlip,\n            setBetType,\n            placeBets,\n            updateOdds\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\contexts\\\\BettingContext.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\nfunction useBetting() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BettingContext);\n    if (context === undefined) {\n        throw new Error(\"useBetting must be used within a BettingProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BettingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/bettingValidation.ts":
/*!****************************************!*\
  !*** ./src/utils/bettingValidation.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeBettingPattern: () => (/* binding */ analyzeBettingPattern),\n/* harmony export */   formatValidationErrors: () => (/* binding */ formatValidationErrors),\n/* harmony export */   groupValidationErrors: () => (/* binding */ groupValidationErrors),\n/* harmony export */   validateBetAmount: () => (/* binding */ validateBetAmount),\n/* harmony export */   validateBetSlip: () => (/* binding */ validateBetSlip),\n/* harmony export */   validateOddsChange: () => (/* binding */ validateOddsChange),\n/* harmony export */   validateResponsibleGambling: () => (/* binding */ validateResponsibleGambling)\n/* harmony export */ });\nconst validateBetAmount = (amount, limits)=>{\n    const errors = [];\n    if (amount <= 0) {\n        errors.push({\n            field: \"amount\",\n            message: \"Bet amount must be greater than 0\",\n            code: \"INVALID_AMOUNT\"\n        });\n    }\n    if (amount < limits.minBet) {\n        errors.push({\n            field: \"amount\",\n            message: `Minimum bet amount is €${limits.minBet}`,\n            code: \"BELOW_MIN_BET\"\n        });\n    }\n    if (amount > limits.maxBet) {\n        errors.push({\n            field: \"amount\",\n            message: `Maximum bet amount is €${limits.maxBet}`,\n            code: \"ABOVE_MAX_BET\"\n        });\n    }\n    return errors;\n};\nconst validateBetSlip = (items, userBalance, limits, betType)=>{\n    const errors = [];\n    if (items.length === 0) {\n        errors.push({\n            field: \"betSlip\",\n            message: \"No bets selected\",\n            code: \"EMPTY_BET_SLIP\"\n        });\n        return errors;\n    }\n    // Validate individual bet amounts\n    items.forEach((item, index)=>{\n        const amountErrors = validateBetAmount(item.amount, limits);\n        amountErrors.forEach((error)=>{\n            errors.push({\n                ...error,\n                field: `items[${index}].amount`\n            });\n        });\n    });\n    // Validate total stake vs balance\n    const totalStake = items.reduce((sum, item)=>sum + item.amount, 0);\n    if (totalStake > userBalance) {\n        errors.push({\n            field: \"totalStake\",\n            message: `Insufficient balance. You have €${userBalance.toFixed(2)} but need €${totalStake.toFixed(2)}`,\n            code: \"INSUFFICIENT_BALANCE\"\n        });\n    }\n    // Validate parlay constraints\n    if (betType === \"parlay\") {\n        if (items.length < 2) {\n            errors.push({\n                field: \"betType\",\n                message: \"Parlay bets require at least 2 selections\",\n                code: \"INSUFFICIENT_PARLAY_LEGS\"\n            });\n        }\n        if (items.length > limits.maxParlayLegs) {\n            errors.push({\n                field: \"betType\",\n                message: `Maximum ${limits.maxParlayLegs} selections allowed in parlay`,\n                code: \"TOO_MANY_PARLAY_LEGS\"\n            });\n        }\n        // Check for conflicting selections (same match)\n        const matchIds = items.map((item)=>item.matchId);\n        const uniqueMatchIds = new Set(matchIds);\n        if (matchIds.length !== uniqueMatchIds.size) {\n            errors.push({\n                field: \"selections\",\n                message: \"Cannot have multiple selections from the same match in a parlay\",\n                code: \"CONFLICTING_SELECTIONS\"\n            });\n        }\n    }\n    // Validate potential payout\n    const totalPotentialWin = betType === \"parlay\" ? totalStake * items.reduce((odds, item)=>odds * item.selection.odds, 1) : items.reduce((sum, item)=>sum + item.potentialWin, 0);\n    if (totalPotentialWin > limits.maxPayout) {\n        errors.push({\n            field: \"potentialWin\",\n            message: `Maximum payout is €${limits.maxPayout}. Current potential win: €${totalPotentialWin.toFixed(2)}`,\n            code: \"EXCEEDS_MAX_PAYOUT\"\n        });\n    }\n    return errors;\n};\nconst validateOddsChange = (originalOdds, currentOdds, tolerance = 0.1)=>{\n    const change = Math.abs(originalOdds - currentOdds);\n    const percentageChange = change / originalOdds;\n    return percentageChange <= tolerance;\n};\nconst formatValidationErrors = (errors)=>{\n    return errors.map((error)=>error.message);\n};\nconst groupValidationErrors = (errors)=>{\n    const grouped = {};\n    errors.forEach((error)=>{\n        const field = error.field.split(\"[\")[0]; // Group array fields together\n        if (!grouped[field]) {\n            grouped[field] = [];\n        }\n        grouped[field].push(error);\n    });\n    return grouped;\n};\n// Responsible gambling validation\nconst validateResponsibleGambling = (amount, dailySpent, weeklySpent, monthlySpent, limits)=>{\n    const errors = [];\n    if (limits.daily && dailySpent + amount > limits.daily) {\n        errors.push({\n            field: \"dailyLimit\",\n            message: `This bet would exceed your daily spending limit of €${limits.daily}`,\n            code: \"EXCEEDS_DAILY_LIMIT\"\n        });\n    }\n    if (limits.weekly && weeklySpent + amount > limits.weekly) {\n        errors.push({\n            field: \"weeklyLimit\",\n            message: `This bet would exceed your weekly spending limit of €${limits.weekly}`,\n            code: \"EXCEEDS_WEEKLY_LIMIT\"\n        });\n    }\n    if (limits.monthly && monthlySpent + amount > limits.monthly) {\n        errors.push({\n            field: \"monthlyLimit\",\n            message: `This bet would exceed your monthly spending limit of €${limits.monthly}`,\n            code: \"EXCEEDS_MONTHLY_LIMIT\"\n        });\n    }\n    return errors;\n};\n// Betting pattern analysis\nconst analyzeBettingPattern = (recentBets, timeWindow = 24 * 60 * 60 * 1000 // 24 hours\n)=>{\n    const now = Date.now();\n    const recentBetsInWindow = recentBets.filter((bet)=>now - bet.timestamp.getTime() <= timeWindow);\n    const warnings = [];\n    const recommendations = [];\n    // Check for rapid betting\n    if (recentBetsInWindow.length > 10) {\n        warnings.push(\"You have placed many bets recently\");\n        recommendations.push(\"Consider taking a break from betting\");\n    }\n    // Check for increasing bet amounts\n    const amounts = recentBetsInWindow.map((bet)=>bet.amount);\n    if (amounts.length >= 3) {\n        const isIncreasing = amounts.slice(-3).every((amount, index, arr)=>index === 0 || amount > arr[index - 1]);\n        if (isIncreasing) {\n            warnings.push(\"Your bet amounts have been increasing\");\n            recommendations.push(\"Consider setting a maximum bet limit\");\n        }\n    }\n    // Check for chasing losses\n    const lostBets = recentBetsInWindow.filter((bet)=>bet.status === \"lost\");\n    if (lostBets.length >= 3) {\n        const lastThreeBets = recentBetsInWindow.slice(-3);\n        const allLosses = lastThreeBets.every((bet)=>bet.status === \"lost\");\n        if (allLosses) {\n            warnings.push(\"You have had several losses in a row\");\n            recommendations.push(\"Consider taking a break to avoid chasing losses\");\n        }\n    }\n    return {\n        isRiskyPattern: warnings.length > 0,\n        warnings,\n        recommendations\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/bettingValidation.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0be59598e783\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFoc2luYmV0LXdlYnNpdGUvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzU4Y2UiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwYmU1OTU5OGU3ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"TahsinBet - Modern Sports Betting Platform\",\n    description: \"Experience the future of sports betting with TahsinBet. Live betting, competitive odds, and secure transactions.\",\n    keywords: \"sports betting, live betting, football, basketball, tennis, casino, odds\",\n    authors: [\n        {\n            name: \"TahsinBet Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/live/page.tsx":
/*!*******************************!*\
  !*** ./src/app/live/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\TahsinBet Yeni Website\src\app\live\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flive%2Fpage&page=%2Flive%2Fpage&appPaths=%2Flive%2Fpage&pagePath=private-next-app-dir%2Flive%2Fpage.tsx&appDir=D%3A%5CTahsinBet%20Yeni%20Website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTahsinBet%20Yeni%20Website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();