import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'TahsinBet - Modern Sports Betting Platform',
  description: 'Experience the future of sports betting with TahsinBet. Live betting, competitive odds, and secure transactions.',
  keywords: 'sports betting, live betting, football, basketball, tennis, casino, odds',
  authors: [{ name: 'TahsinBet Team' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {children}
        </div>
      </body>
    </html>
  )
}
