# TahsinBet - Modern Sports Betting Platform

A modern, responsive sports betting website built with Next.js, TypeScript, and Tailwind CSS. Features a sleek design with smooth animations, real-time betting odds, and a comprehensive user experience.

## 🚀 Features

### Core Functionality
- **Homepage** - Hero section with platform showcase and statistics
- **Sports Betting** - Browse and filter sports events with live odds
- **Live Betting** - Real-time betting interface with live match updates
- **User Authentication** - Login and registration with form validation
- **User Dashboard** - Personal betting statistics and account management

### Comprehensive Betting System
- **Bet Placement** - Interactive betting slip with multiple bet support
- **Multiple Bet Types** - Win/Draw/Lose, Over/Under, Handicap, Both Teams Score
- **Live Odds Updates** - Real-time odds changes with visual indicators
- **Parlay Betting** - Accumulator bets with combined odds calculation
- **Cash Out** - Live cash out functionality for pending bets
- **Bet Validation** - Comprehensive validation with balance and limit checks
- **Responsible Gambling** - Built-in limits and pattern analysis

### Design & UX
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile
- **Modern UI** - Glass morphism effects, gradients, and smooth animations
- **Interactive Elements** - Hover effects, transitions, and micro-interactions
- **Loading States** - Professional loading animations and spinners
- **Form Validation** - Real-time validation with smooth error animations

### Technical Features
- **Next.js 14** - Latest version with App Router
- **TypeScript** - Full type safety throughout the application
- **Tailwind CSS** - Utility-first CSS framework for rapid styling
- **Framer Motion** - Smooth animations and page transitions
- **Lucide React** - Beautiful, customizable icons
- **Responsive Navigation** - Mobile-friendly header and navigation

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **State Management**: React Context API
- **Development**: ESLint, PostCSS

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   │   ├── login/         # Login page
│   │   └── register/      # Registration page
│   ├── dashboard/         # User dashboard
│   ├── live/              # Live betting page
│   ├── sports/            # Sports betting page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/            # Reusable components
│   ├── Layout/           # Layout components
│   │   ├── Header.tsx    # Navigation header
│   │   ├── Footer.tsx    # Footer component
│   │   └── Layout.tsx    # Main layout wrapper
│   ├── UI/               # UI components
│   │   ├── Button.tsx    # Button component
│   │   ├── Card.tsx      # Card component
│   │   └── LoadingSpinner.tsx # Loading spinner
│   ├── Betting/          # Betting system components
│   │   ├── BetSlip.tsx   # Betting slip sidebar
│   │   ├── BetButton.tsx # Individual bet buttons
│   │   ├── BettingMarkets.tsx # Multiple betting markets
│   │   ├── CashOut.tsx   # Cash out functionality
│   │   └── BetSlipToggle.tsx # Mobile bet slip toggle
│   ├── Auth/             # Authentication components
│   ├── Sports/           # Sports-related components
│   ├── Live/             # Live betting components
│   └── Dashboard/        # Dashboard components
├── contexts/             # React Context providers
│   └── BettingContext.tsx # Betting state management
├── types/                # TypeScript type definitions
│   └── index.ts          # Main types file
└── utils/                # Utility functions
    ├── bettingApi.ts     # Mock betting API functions
    └── bettingValidation.ts # Betting validation utilities
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tahsinbet-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🎨 Design Features

### Color Scheme
- **Primary**: Blue gradient (from-blue-600 to-purple-600)
- **Background**: Dark gradient (slate-900 via-purple-900)
- **Glass Effects**: Semi-transparent backgrounds with backdrop blur
- **Status Colors**: Green (success), Red (error), Yellow (warning)

### Animations
- **Page Transitions**: Smooth fade-in and slide-up animations
- **Hover Effects**: Scale and shadow transformations
- **Loading States**: Spinning animations and pulse effects
- **Form Interactions**: Real-time validation feedback

### Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎰 Betting System Features

### Bet Placement System
- **Interactive Betting Slip**: Persistent sidebar with bet management
- **Quick Bet Amounts**: €10, €25, €50, €100 quick selection buttons
- **Real-time Calculations**: Automatic potential winnings calculation
- **Bet Confirmation**: Modal with terms acceptance and final review
- **Mobile Optimized**: Floating bet slip toggle for mobile devices

### Betting Markets
- **Match Result**: Win/Draw/Lose betting with team-specific styling
- **Over/Under**: Multiple goal line options (2.5, 3.5, etc.)
- **Asian Handicap**: Handicap betting with various line options
- **Both Teams Score**: Yes/No betting market
- **Live Markets**: Real-time odds updates during live matches

### Advanced Betting Features
- **Parlay/Accumulator**: Combine multiple bets with multiplied odds
- **Live Cash Out**: Real-time cash out values for pending bets
- **Odds Movement**: Visual indicators for odds increases/decreases
- **Bet Validation**: Comprehensive validation with helpful error messages
- **Balance Management**: Real-time balance updates and insufficient funds warnings

### Responsible Gambling
- **Betting Limits**: Configurable minimum and maximum bet amounts
- **Daily/Weekly Limits**: Spending limit tracking and warnings
- **Pattern Analysis**: Detection of risky betting behaviors
- **Self-Exclusion**: Tools for responsible gambling management
- **Terms Acceptance**: Required terms and conditions acceptance

## 📱 Pages Overview

### Homepage (`/`)
- Hero section with call-to-action
- Platform statistics
- Feature highlights
- Popular sports showcase
- Newsletter signup

### Sports Betting (`/sports`)
- Sports category filter
- Match listings with interactive bet buttons
- Search functionality
- Expandable betting markets
- Real-time odds updates

### Live Betting (`/live`)
- Live match selection
- Real-time score updates
- Live odds and betting options
- Additional betting markets
- Match statistics and events

### Authentication (`/auth/login`, `/auth/register`)
- Secure login/registration forms
- Form validation with error handling
- Social login options
- Password strength indicator

### Dashboard (`/dashboard`)
- User statistics overview
- Active bets with cash out options
- Recent betting history
- Upcoming matches
- Quick actions panel

## 🔧 Customization

### Adding New Sports
1. Update the sports array in relevant components
2. Add sport-specific icons and styling
3. Update TypeScript types if needed

### Modifying Animations
- Edit animation variants in component files
- Adjust Tailwind animation classes
- Customize Framer Motion transitions

### Styling Changes
- Update Tailwind config for theme changes
- Modify CSS custom properties in globals.css
- Adjust component-specific styles

## 🚀 Deployment

### Build for Production
```bash
npm run build
npm run start
```

### Deploy to Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Deploy to Other Platforms
- **Netlify**: Build command `npm run build`, publish directory `out`
- **AWS/Azure**: Use Docker or static hosting
- **Self-hosted**: Use PM2 or similar process manager

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Project Wiki]
- Issues: [GitHub Issues]

---

**Note**: This is a demo betting website for educational purposes. Please ensure compliance with local gambling laws and regulations before deploying for commercial use.
