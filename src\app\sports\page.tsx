'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Clock, TrendingUp } from 'lucide-react';
import Layout from '@/components/Layout/Layout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import BetButton from '@/components/Betting/BetButton';
import BettingMarkets from '@/components/Betting/BettingMarkets';

export default function SportsPage() {
  const [selectedSport, setSelectedSport] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const sports = [
    { id: 'all', name: 'All Sports', icon: '🏆', count: 156 },
    { id: 'football', name: 'Football', icon: '⚽', count: 45 },
    { id: 'basketball', name: 'Basketball', icon: '🏀', count: 32 },
    { id: 'tennis', name: 'Tennis', icon: '🎾', count: 28 },
    { id: 'baseball', name: 'Baseball', icon: '⚾', count: 18 },
    { id: 'hockey', name: 'Hockey', icon: '🏒', count: 15 },
    { id: 'soccer', name: 'Soccer', icon: '⚽', count: 12 },
    { id: 'golf', name: 'Golf', icon: '⛳', count: 6 },
  ];

  const matches = [
    {
      id: 1,
      sport: 'football',
      league: 'Premier League',
      homeTeam: 'Manchester United',
      awayTeam: 'Liverpool',
      homeOdds: 2.45,
      awayOdds: 2.80,
      drawOdds: 3.20,
      startTime: '2024-01-15T15:00:00Z',
      isLive: false,
    },
    {
      id: 2,
      sport: 'basketball',
      league: 'NBA',
      homeTeam: 'Lakers',
      awayTeam: 'Warriors',
      homeOdds: 1.95,
      awayOdds: 1.85,
      startTime: '2024-01-15T20:00:00Z',
      isLive: true,
      score: { home: 78, away: 82 },
    },
    {
      id: 3,
      sport: 'tennis',
      league: 'ATP Masters',
      homeTeam: 'Novak Djokovic',
      awayTeam: 'Rafael Nadal',
      homeOdds: 1.75,
      awayOdds: 2.10,
      startTime: '2024-01-15T14:30:00Z',
      isLive: false,
    },
    {
      id: 4,
      sport: 'football',
      league: 'La Liga',
      homeTeam: 'Real Madrid',
      awayTeam: 'Barcelona',
      homeOdds: 2.20,
      awayOdds: 3.10,
      drawOdds: 3.40,
      startTime: '2024-01-15T21:00:00Z',
      isLive: false,
    },
  ];

  const filteredMatches = matches.filter(match => {
    const matchesSport = selectedSport === 'all' || match.sport === selectedSport;
    const matchesSearch = searchTerm === '' || 
      match.homeTeam.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.awayTeam.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.league.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSport && matchesSearch;
  });

  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Sports Betting
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Discover the best odds across all major sports and leagues worldwide.
            </p>
          </motion.div>

          {/* Search and Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <Card glass className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search teams, leagues, or matches..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-blue-500"
                  />
                </div>
                <Button variant="outline" className="border-white/30 text-white hover:bg-white/10">
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </Button>
              </div>
            </Card>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sports Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-1"
            >
              <Card glass className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Sports</h3>
                <div className="space-y-2">
                  {sports.map((sport) => (
                    <button
                      key={sport.id}
                      onClick={() => setSelectedSport(sport.id)}
                      className={`w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 ${
                        selectedSport === sport.id
                          ? 'bg-blue-600 text-white'
                          : 'text-white/80 hover:bg-white/10'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{sport.icon}</span>
                        <span className="font-medium">{sport.name}</span>
                      </div>
                      <span className="text-sm opacity-75">{sport.count}</span>
                    </button>
                  ))}
                </div>
              </Card>
            </motion.div>

            {/* Matches List */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="lg:col-span-3"
            >
              <div className="space-y-4">
                {filteredMatches.map((match, index) => (
                  <motion.div
                    key={match.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <Card glass hover className="p-6">
                      <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                        <div className="flex-1 mb-4 md:mb-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm text-blue-400 font-medium">
                              {match.league}
                            </span>
                            {match.isLive && (
                              <span className="flex items-center space-x-1 text-red-400 text-sm">
                                <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                                <span>LIVE</span>
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-4">
                            <div className="text-white font-semibold">
                              {match.homeTeam}
                            </div>
                            <div className="text-white/60">vs</div>
                            <div className="text-white font-semibold">
                              {match.awayTeam}
                            </div>
                          </div>
                          
                          {match.isLive && match.score && (
                            <div className="text-2xl font-bold text-white mt-2">
                              {match.score.home} - {match.score.away}
                            </div>
                          )}
                          
                          {!match.isLive && (
                            <div className="flex items-center space-x-1 text-white/60 text-sm mt-2">
                              <Clock className="w-4 h-4" />
                              <span>{formatTime(match.startTime)}</span>
                            </div>
                          )}
                        </div>

                        <div className="flex space-x-2">
                          <BetButton
                            matchId={match.id.toString()}
                            match={{
                              homeTeam: match.homeTeam,
                              awayTeam: match.awayTeam,
                              league: match.league,
                              startTime: new Date(match.startTime),
                              isLive: match.isLive,
                            }}
                            selection={{
                              type: 'home',
                              label: match.homeTeam,
                              odds: match.homeOdds,
                            }}
                            variant="home"
                            size="sm"
                          />
                          {match.drawOdds && (
                            <BetButton
                              matchId={match.id.toString()}
                              match={{
                                homeTeam: match.homeTeam,
                                awayTeam: match.awayTeam,
                                league: match.league,
                                startTime: new Date(match.startTime),
                                isLive: match.isLive,
                              }}
                              selection={{
                                type: 'draw',
                                label: 'Draw',
                                odds: match.drawOdds,
                              }}
                              variant="draw"
                              size="sm"
                            />
                          )}
                          <BetButton
                            matchId={match.id.toString()}
                            match={{
                              homeTeam: match.homeTeam,
                              awayTeam: match.awayTeam,
                              league: match.league,
                              startTime: new Date(match.startTime),
                              isLive: match.isLive,
                            }}
                            selection={{
                              type: 'away',
                              label: match.awayTeam,
                              odds: match.awayOdds,
                            }}
                            variant="away"
                            size="sm"
                          />
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {filteredMatches.length === 0 && (
                <Card glass className="p-12 text-center">
                  <div className="text-white/60 text-lg">
                    No matches found for your search criteria.
                  </div>
                </Card>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
