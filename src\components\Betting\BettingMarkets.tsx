'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import BetButton from './BetButton';
import Card from '@/components/UI/Card';

interface Match {
  id: string;
  homeTeam: string;
  awayTeam: string;
  league: string;
  startTime: Date;
  isLive: boolean;
}

interface BettingMarketsProps {
  match: Match;
  markets?: {
    matchResult?: {
      home: number;
      draw?: number;
      away: number;
    };
    overUnder?: {
      line: number;
      over: number;
      under: number;
    }[];
    handicap?: {
      line: number;
      home: number;
      away: number;
    }[];
    bothTeamsScore?: {
      yes: number;
      no: number;
    };
  };
  expanded?: boolean;
}

const BettingMarkets = ({ 
  match, 
  markets = {}, 
  expanded = false 
}: BettingMarketsProps) => {
  const [isExpanded, setIsExpanded] = useState(expanded);

  // Default markets if not provided
  const defaultMarkets = {
    matchResult: {
      home: 2.45,
      draw: 3.20,
      away: 2.80,
    },
    overUnder: [
      { line: 2.5, over: 1.85, under: 1.95 },
      { line: 3.5, over: 2.80, under: 1.40 },
    ],
    handicap: [
      { line: -1, home: 3.20, away: 1.35 },
      { line: 0, home: 2.10, away: 1.75 },
      { line: +1, home: 1.45, away: 2.75 },
    ],
    bothTeamsScore: {
      yes: 1.65,
      no: 2.25,
    },
    ...markets,
  };

  const marketSections = [
    {
      id: 'match-result',
      title: 'Match Result',
      content: (
        <div className="grid grid-cols-3 gap-2">
          <BetButton
            matchId={match.id}
            match={match}
            selection={{
              type: 'home',
              label: match.homeTeam,
              odds: defaultMarkets.matchResult.home,
            }}
            variant="home"
            size="md"
          />
          {defaultMarkets.matchResult.draw && (
            <BetButton
              matchId={match.id}
              match={match}
              selection={{
                type: 'draw',
                label: 'Draw',
                odds: defaultMarkets.matchResult.draw,
              }}
              variant="draw"
              size="md"
            />
          )}
          <BetButton
            matchId={match.id}
            match={match}
            selection={{
              type: 'away',
              label: match.awayTeam,
              odds: defaultMarkets.matchResult.away,
            }}
            variant="away"
            size="md"
          />
        </div>
      ),
    },
    {
      id: 'over-under',
      title: 'Total Goals',
      content: (
        <div className="space-y-2">
          {defaultMarkets.overUnder.map((market, index) => (
            <div key={index} className="grid grid-cols-3 gap-2 items-center">
              <div className="text-white/60 text-sm text-center">
                O/U {market.line}
              </div>
              <BetButton
                matchId={match.id}
                match={match}
                selection={{
                  type: 'over',
                  label: `Over ${market.line}`,
                  odds: market.over,
                  line: market.line,
                }}
                variant="over"
                size="sm"
              />
              <BetButton
                matchId={match.id}
                match={match}
                selection={{
                  type: 'under',
                  label: `Under ${market.line}`,
                  odds: market.under,
                  line: market.line,
                }}
                variant="under"
                size="sm"
              />
            </div>
          ))}
        </div>
      ),
    },
    {
      id: 'handicap',
      title: 'Asian Handicap',
      content: (
        <div className="space-y-2">
          {defaultMarkets.handicap.map((market, index) => (
            <div key={index} className="grid grid-cols-3 gap-2 items-center">
              <div className="text-white/60 text-sm text-center">
                {market.line > 0 ? `+${market.line}` : market.line}
              </div>
              <BetButton
                matchId={match.id}
                match={match}
                selection={{
                  type: 'handicap',
                  label: `${match.homeTeam} ${market.line > 0 ? `+${market.line}` : market.line}`,
                  odds: market.home,
                  line: market.line,
                }}
                variant="home"
                size="sm"
              />
              <BetButton
                matchId={match.id}
                match={match}
                selection={{
                  type: 'handicap',
                  label: `${match.awayTeam} ${market.line < 0 ? `+${Math.abs(market.line)}` : `-${market.line}`}`,
                  odds: market.away,
                  line: -market.line,
                }}
                variant="away"
                size="sm"
              />
            </div>
          ))}
        </div>
      ),
    },
    {
      id: 'both-teams-score',
      title: 'Both Teams to Score',
      content: (
        <div className="grid grid-cols-2 gap-2">
          <BetButton
            matchId={match.id}
            match={match}
            selection={{
              type: 'yes' as any,
              label: 'Yes',
              odds: defaultMarkets.bothTeamsScore.yes,
            }}
            variant="over"
            size="md"
          />
          <BetButton
            matchId={match.id}
            match={match}
            selection={{
              type: 'no' as any,
              label: 'No',
              odds: defaultMarkets.bothTeamsScore.no,
            }}
            variant="under"
            size="md"
          />
        </div>
      ),
    },
  ];

  return (
    <Card glass className="p-4">
      {/* Main Market - Always Visible */}
      <div className="mb-4">
        <h4 className="text-white font-medium mb-3">Match Result</h4>
        {marketSections[0].content}
      </div>

      {/* Expand/Collapse Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-center space-x-2 py-2 text-white/60 hover:text-white transition-colors border-t border-white/10"
      >
        <span className="text-sm">
          {isExpanded ? 'Less Markets' : 'More Markets'}
        </span>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </button>

      {/* Additional Markets */}
      <motion.div
        initial={false}
        animate={{
          height: isExpanded ? 'auto' : 0,
          opacity: isExpanded ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="pt-4 space-y-6">
          {marketSections.slice(1).map((section, index) => (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <h4 className="text-white font-medium mb-3">{section.title}</h4>
              {section.content}
            </motion.div>
          ))}
        </div>
      </motion.div>
    </Card>
  );
};

export default BettingMarkets;
