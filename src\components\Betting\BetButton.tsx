'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { useBetting } from '@/contexts/BettingContext';
import { BetSlipItem } from '@/types';

interface BetButtonProps {
  matchId: string;
  match: {
    homeTeam: string;
    awayTeam: string;
    league: string;
    startTime: Date;
    isLive: boolean;
  };
  selection: {
    type: 'home' | 'away' | 'draw' | 'over' | 'under' | 'handicap';
    label: string;
    odds: number;
    line?: number;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'home' | 'away' | 'draw' | 'over' | 'under';
}

const BetButton = ({
  matchId,
  match,
  selection,
  className = '',
  size = 'md',
  variant = 'default',
}: BetButtonProps) => {
  const { state, addToBetSlip } = useBetting();
  const [previousOdds, setPreviousOdds] = useState(selection.odds);
  const [oddsDirection, setOddsDirection] = useState<'up' | 'down' | null>(null);

  // Check if this selection is already in the bet slip
  const isInSlip = state.betSlip.items.some(
    item => item.matchId === matchId && item.selection.type === selection.type
  );

  // Detect odds changes
  if (selection.odds !== previousOdds) {
    setOddsDirection(selection.odds > previousOdds ? 'up' : 'down');
    setPreviousOdds(selection.odds);
    
    // Clear direction after animation
    setTimeout(() => setOddsDirection(null), 1000);
  }

  const handleClick = () => {
    const betSlipItem: BetSlipItem = {
      id: `${matchId}-${selection.type}`,
      matchId,
      match,
      selection,
      amount: 0,
      potentialWin: 0,
    };

    addToBetSlip(betSlipItem);
  };

  const getVariantClasses = () => {
    const baseClasses = 'relative overflow-hidden transition-all duration-200 font-semibold rounded-lg border-2';
    
    switch (variant) {
      case 'home':
        return `${baseClasses} ${
          isInSlip
            ? 'bg-green-600 border-green-500 text-white'
            : 'bg-green-500/10 border-green-500/50 text-green-400 hover:bg-green-500/20'
        }`;
      case 'away':
        return `${baseClasses} ${
          isInSlip
            ? 'bg-red-600 border-red-500 text-white'
            : 'bg-red-500/10 border-red-500/50 text-red-400 hover:bg-red-500/20'
        }`;
      case 'draw':
        return `${baseClasses} ${
          isInSlip
            ? 'bg-yellow-600 border-yellow-500 text-white'
            : 'bg-yellow-500/10 border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/20'
        }`;
      case 'over':
        return `${baseClasses} ${
          isInSlip
            ? 'bg-blue-600 border-blue-500 text-white'
            : 'bg-blue-500/10 border-blue-500/50 text-blue-400 hover:bg-blue-500/20'
        }`;
      case 'under':
        return `${baseClasses} ${
          isInSlip
            ? 'bg-purple-600 border-purple-500 text-white'
            : 'bg-purple-500/10 border-purple-500/50 text-purple-400 hover:bg-purple-500/20'
        }`;
      default:
        return `${baseClasses} ${
          isInSlip
            ? 'bg-blue-600 border-blue-500 text-white'
            : 'bg-white/10 border-white/20 text-white hover:bg-white/20'
        }`;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs min-w-[60px]';
      case 'lg':
        return 'px-6 py-3 text-base min-w-[100px]';
      default:
        return 'px-4 py-2 text-sm min-w-[80px]';
    }
  };

  return (
    <motion.button
      onClick={handleClick}
      className={`${getVariantClasses()} ${getSizeClasses()} ${className}`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      layout
    >
      {/* Odds change indicator */}
      {oddsDirection && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0 }}
          className={`absolute top-0 right-0 w-3 h-3 ${
            oddsDirection === 'up' ? 'text-green-400' : 'text-red-400'
          }`}
        >
          {oddsDirection === 'up' ? (
            <TrendingUp className="w-3 h-3" />
          ) : (
            <TrendingDown className="w-3 h-3" />
          )}
        </motion.div>
      )}

      {/* Live indicator */}
      {match.isLive && (
        <div className="absolute top-1 left-1 w-2 h-2 bg-red-400 rounded-full animate-pulse" />
      )}

      {/* Odds display */}
      <motion.div
        key={selection.odds}
        initial={{ scale: 1.1 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.2 }}
        className="flex flex-col items-center"
      >
        {size !== 'sm' && (
          <div className="text-xs opacity-80 mb-1 truncate max-w-full">
            {selection.label}
          </div>
        )}
        <div className="font-bold">
          {selection.odds.toFixed(2)}
        </div>
      </motion.div>

      {/* Selection indicator */}
      {isInSlip && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute inset-0 bg-white/20 rounded-lg flex items-center justify-center"
        >
          <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-current rounded-full" />
          </div>
        </motion.div>
      )}

      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 bg-white/20 rounded-lg"
        initial={{ scale: 0, opacity: 0.5 }}
        animate={{ scale: 1.5, opacity: 0 }}
        transition={{ duration: 0.6 }}
        key={`ripple-${selection.odds}`}
      />
    </motion.button>
  );
};

export default BetButton;
