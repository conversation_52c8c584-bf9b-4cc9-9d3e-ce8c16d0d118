"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/live/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-down.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TrendingDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst TrendingDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingDown\", [\n  [\"polyline\", { points: \"22 17 13.5 8.5 8.5 13.5 2 7\", key: \"1r2t7k\" }],\n  [\"polyline\", { points: \"16 17 22 17 22 11\", key: \"11uiuu\" }]\n]);\n\n\n//# sourceMappingURL=trending-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJlbmRpbmctZG93bi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELHFCQUFxQixnRUFBZ0I7QUFDckMsaUJBQWlCLHNEQUFzRDtBQUN2RSxpQkFBaUIsNENBQTRDO0FBQzdEOztBQUVtQztBQUNuQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3RyZW5kaW5nLWRvd24uanM/ZDk2YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFRyZW5kaW5nRG93biA9IGNyZWF0ZUx1Y2lkZUljb24oXCJUcmVuZGluZ0Rvd25cIiwgW1xuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCIyMiAxNyAxMy41IDguNSA4LjUgMTMuNSAyIDdcIiwga2V5OiBcIjFyMnQ3a1wiIH1dLFxuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCIxNiAxNyAyMiAxNyAyMiAxMVwiLCBrZXk6IFwiMTF1aXV1XCIgfV1cbl0pO1xuXG5leHBvcnQgeyBUcmVuZGluZ0Rvd24gYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJlbmRpbmctZG93bi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/live/page.tsx":
/*!*******************************!*\
  !*** ./src/app/live/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LiveBettingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/Layout */ \"(app-pages-browser)/./src/components/Layout/Layout.tsx\");\n/* harmony import */ var _components_UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UI/Card */ \"(app-pages-browser)/./src/components/UI/Card.tsx\");\n/* harmony import */ var _components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Betting/BetButton */ \"(app-pages-browser)/./src/components/Betting/BetButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LiveBettingPage() {\n    _s();\n    const [selectedMatch, setSelectedMatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    const liveMatches = [\n        {\n            id: 1,\n            sport: \"Football\",\n            league: \"Premier League\",\n            homeTeam: \"Manchester City\",\n            awayTeam: \"Arsenal\",\n            score: {\n                home: 2,\n                away: 1\n            },\n            time: \"67'\",\n            homeOdds: {\n                win: 1.45,\n                draw: 4.20,\n                lose: 6.50\n            },\n            awayOdds: {\n                win: 6.50,\n                draw: 4.20,\n                lose: 1.45\n            },\n            events: [\n                {\n                    time: \"65'\",\n                    type: \"goal\",\n                    team: \"home\",\n                    player: \"Haaland\"\n                },\n                {\n                    time: \"43'\",\n                    type: \"goal\",\n                    team: \"away\",\n                    player: \"Saka\"\n                },\n                {\n                    time: \"23'\",\n                    type: \"goal\",\n                    team: \"home\",\n                    player: \"De Bruyne\"\n                }\n            ],\n            stats: {\n                possession: {\n                    home: 58,\n                    away: 42\n                },\n                shots: {\n                    home: 12,\n                    away: 8\n                },\n                corners: {\n                    home: 6,\n                    away: 3\n                }\n            }\n        },\n        {\n            id: 2,\n            sport: \"Basketball\",\n            league: \"NBA\",\n            homeTeam: \"Lakers\",\n            awayTeam: \"Warriors\",\n            score: {\n                home: 89,\n                away: 94\n            },\n            time: \"Q3 8:45\",\n            homeOdds: {\n                win: 2.10,\n                spread: 1.90\n            },\n            awayOdds: {\n                win: 1.75,\n                spread: 1.90\n            },\n            events: [\n                {\n                    time: \"9:12\",\n                    type: \"score\",\n                    team: \"away\",\n                    player: \"Curry 3PT\"\n                },\n                {\n                    time: \"9:45\",\n                    type: \"score\",\n                    team: \"home\",\n                    player: \"LeBron 2PT\"\n                }\n            ],\n            stats: {\n                fieldGoal: {\n                    home: \"45%\",\n                    away: \"52%\"\n                },\n                threePoint: {\n                    home: \"38%\",\n                    away: \"41%\"\n                },\n                rebounds: {\n                    home: 28,\n                    away: 31\n                }\n            }\n        },\n        {\n            id: 3,\n            sport: \"Tennis\",\n            league: \"ATP Masters\",\n            homeTeam: \"Djokovic\",\n            awayTeam: \"Alcaraz\",\n            score: {\n                home: \"6-4, 3-2\",\n                away: \"\"\n            },\n            time: \"Set 2\",\n            homeOdds: {\n                win: 1.65\n            },\n            awayOdds: {\n                win: 2.25\n            },\n            events: [\n                {\n                    time: \"Game 5\",\n                    type: \"break\",\n                    team: \"home\",\n                    player: \"Djokovic breaks\"\n                },\n                {\n                    time: \"Game 3\",\n                    type: \"ace\",\n                    team: \"away\",\n                    player: \"Alcaraz ace\"\n                }\n            ],\n            stats: {\n                aces: {\n                    home: 8,\n                    away: 12\n                },\n                winners: {\n                    home: 15,\n                    away: 18\n                },\n                unforced: {\n                    home: 7,\n                    away: 11\n                }\n            }\n        }\n    ];\n    const currentMatch = liveMatches[selectedMatch];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                                children: \"Live Betting\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80\",\n                                children: \"Bet on live matches with real-time odds and instant updates\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    glass: true,\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Live Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 text-red-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-red-400 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"LIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: liveMatches.map((match, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedMatch(index),\n                                                    className: \"w-full p-3 rounded-lg transition-all duration-200 text-left \".concat(selectedMatch === index ? \"bg-blue-600 text-white\" : \"bg-white/5 text-white/80 hover:bg-white/10\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-400 mb-1\",\n                                                            children: match.league\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: [\n                                                                match.homeTeam,\n                                                                \" vs \",\n                                                                match.awayTeam\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1 opacity-75\",\n                                                            children: match.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, match.id, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.3\n                                },\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    glass: true,\n                                    className: \"p-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-red-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 bg-red-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: \"LIVE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-medium\",\n                                                            children: currentMatch.league\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                                        className: \"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors\",\n                                                        children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-8 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: currentMatch.homeTeam\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: typeof currentMatch.score.home === \"number\" ? currentMatch.score.home : currentMatch.score.home\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/60 mb-2\",\n                                                                children: \"VS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: currentMatch.time\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: currentMatch.awayTeam\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: typeof currentMatch.score.away === \"number\" ? currentMatch.score.away : currentMatch.score.away || \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-green-500/10 border-green-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-400 font-semibold mb-2\",\n                                                            children: [\n                                                                currentMatch.homeTeam,\n                                                                \" Win\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.homeOdds.win\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"home\",\n                                                                label: \"\".concat(currentMatch.homeTeam, \" Win\"),\n                                                                odds: currentMatch.homeOdds.win\n                                                            },\n                                                            variant: \"home\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentMatch.homeOdds.draw && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-yellow-500/10 border-yellow-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-yellow-400 font-semibold mb-2\",\n                                                            children: \"Draw\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.homeOdds.draw\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"draw\",\n                                                                label: \"Draw\",\n                                                                odds: currentMatch.homeOdds.draw\n                                                            },\n                                                            variant: \"draw\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"p-4 text-center bg-red-500/10 border-red-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-red-400 font-semibold mb-2\",\n                                                            children: [\n                                                                currentMatch.awayTeam,\n                                                                \" Win\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: currentMatch.awayOdds.win\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Betting_BetButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            matchId: currentMatch.id.toString(),\n                                                            match: {\n                                                                homeTeam: currentMatch.homeTeam,\n                                                                awayTeam: currentMatch.awayTeam,\n                                                                league: currentMatch.league,\n                                                                startTime: new Date(),\n                                                                isLive: true\n                                                            },\n                                                            selection: {\n                                                                type: \"away\",\n                                                                label: \"\".concat(currentMatch.awayTeam, \" Win\"),\n                                                                odds: currentMatch.awayOdds.win\n                                                            },\n                                                            variant: \"away\",\n                                                            size: \"lg\",\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Recent Events\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: currentMatch.events.slice(0, 3).map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.3,\n                                                                delay: index * 0.1\n                                                            },\n                                                            className: \"flex items-center space-x-3 p-3 bg-white/5 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-400 font-mono text-sm\",\n                                                                    children: event.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-white\",\n                                                                    children: [\n                                                                        event.type === \"goal\" && \"⚽\",\n                                                                        event.type === \"score\" && \"\\uD83C\\uDFC0\",\n                                                                        event.type === \"break\" && \"\\uD83C\\uDFBE\",\n                                                                        event.type === \"ace\" && \"\\uD83C\\uDFBE\",\n                                                                        event.player\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full \".concat(event.team === \"home\" ? \"bg-green-400\" : \"bg-red-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Match Statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: Object.entries(currentMatch.stats).map((param)=>{\n                                                        let [key, value] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/5 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/60 text-sm mb-2 capitalize\",\n                                                                    children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-semibold\",\n                                                                            children: typeof value.home === \"number\" ? value.home : value.home\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-semibold\",\n                                                                            children: typeof value.away === \"number\" ? value.away : value.away\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\app\\\\live\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(LiveBettingPage, \"dMIiH3JGMWJUtBS88imuaxNGw80=\");\n_c = LiveBettingPage;\nvar _c;\n$RefreshReg$(_c, \"LiveBettingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/live/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Betting/BetButton.tsx":
/*!**********************************************!*\
  !*** ./src/components/Betting/BetButton.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BettingContext */ \"(app-pages-browser)/./src/contexts/BettingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst BetButton = (param)=>{\n    let { matchId, match, selection, className = \"\", size = \"md\", variant = \"default\" } = param;\n    _s();\n    const { state, addToBetSlip } = (0,_contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const [previousOdds, setPreviousOdds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selection.odds);\n    const [oddsDirection, setOddsDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if this selection is already in the bet slip\n    const isInSlip = state.betSlip.items.some((item)=>item.matchId === matchId && item.selection.type === selection.type);\n    // Detect odds changes\n    if (selection.odds !== previousOdds) {\n        setOddsDirection(selection.odds > previousOdds ? \"up\" : \"down\");\n        setPreviousOdds(selection.odds);\n        // Clear direction after animation\n        setTimeout(()=>setOddsDirection(null), 1000);\n    }\n    const handleClick = ()=>{\n        const betSlipItem = {\n            id: \"\".concat(matchId, \"-\").concat(selection.type),\n            matchId,\n            match,\n            selection,\n            amount: 0,\n            potentialWin: 0\n        };\n        addToBetSlip(betSlipItem);\n    };\n    const getVariantClasses = ()=>{\n        const baseClasses = \"relative overflow-hidden transition-all duration-200 font-semibold rounded-lg border-2\";\n        switch(variant){\n            case \"home\":\n                return \"\".concat(baseClasses, \" \").concat(isInSlip ? \"bg-green-600 border-green-500 text-white\" : \"bg-green-500/10 border-green-500/50 text-green-400 hover:bg-green-500/20\");\n            case \"away\":\n                return \"\".concat(baseClasses, \" \").concat(isInSlip ? \"bg-red-600 border-red-500 text-white\" : \"bg-red-500/10 border-red-500/50 text-red-400 hover:bg-red-500/20\");\n            case \"draw\":\n                return \"\".concat(baseClasses, \" \").concat(isInSlip ? \"bg-yellow-600 border-yellow-500 text-white\" : \"bg-yellow-500/10 border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/20\");\n            case \"over\":\n                return \"\".concat(baseClasses, \" \").concat(isInSlip ? \"bg-blue-600 border-blue-500 text-white\" : \"bg-blue-500/10 border-blue-500/50 text-blue-400 hover:bg-blue-500/20\");\n            case \"under\":\n                return \"\".concat(baseClasses, \" \").concat(isInSlip ? \"bg-purple-600 border-purple-500 text-white\" : \"bg-purple-500/10 border-purple-500/50 text-purple-400 hover:bg-purple-500/20\");\n            default:\n                return \"\".concat(baseClasses, \" \").concat(isInSlip ? \"bg-blue-600 border-blue-500 text-white\" : \"bg-white/10 border-white/20 text-white hover:bg-white/20\");\n        }\n    };\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"px-2 py-1 text-xs min-w-[60px]\";\n            case \"lg\":\n                return \"px-6 py-3 text-base min-w-[100px]\";\n            default:\n                return \"px-4 py-2 text-sm min-w-[80px]\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n        onClick: handleClick,\n        className: \"\".concat(getVariantClasses(), \" \").concat(getSizeClasses(), \" \").concat(className),\n        whileHover: {\n            scale: 1.02\n        },\n        whileTap: {\n            scale: 0.98\n        },\n        layout: true,\n        children: [\n            oddsDirection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"absolute top-0 right-0 w-3 h-3 \".concat(oddsDirection === \"up\" ? \"text-green-400\" : \"text-red-400\"),\n                children: oddsDirection === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, undefined),\n            match.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1 left-1 w-2 h-2 bg-red-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    scale: 1.1\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.2\n                },\n                className: \"flex flex-col items-center\",\n                children: [\n                    size !== \"sm\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs opacity-80 mb-1 truncate max-w-full\",\n                        children: selection.label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-bold\",\n                        children: selection.odds.toFixed(2)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, selection.odds, true, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            isInSlip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                className: \"absolute inset-0 bg-white/20 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 bg-white rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-current rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 bg-white/20 rounded-lg\",\n                initial: {\n                    scale: 0,\n                    opacity: 0.5\n                },\n                animate: {\n                    scale: 1.5,\n                    opacity: 0\n                },\n                transition: {\n                    duration: 0.6\n                }\n            }, \"ripple-\".concat(selection.odds), false, {\n                fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\TahsinBet Yeni Website\\\\src\\\\components\\\\Betting\\\\BetButton.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BetButton, \"bBL4oU09wd6RrPgVlvErWcrOQgY=\", false, function() {\n    return [\n        _contexts_BettingContext__WEBPACK_IMPORTED_MODULE_2__.useBetting\n    ];\n});\n_c = BetButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BetButton);\nvar _c;\n$RefreshReg$(_c, \"BetButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Betting/BetButton.tsx\n"));

/***/ })

});